package no.ruter.tranop.app.plan.journey.input

import no.ruter.tranop.app.test.AbstractBaseTest
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney

abstract class AbstractDatedJourneyInputTest : AbstractBaseTest() {
    protected fun storeInternalJourney(
        key: String,
        journey: DTODatedJourney,
    ): Int {
        adjustTime(journey)
        val contex = datedJourneyInputService.processInternal(key, journey)
        insightService.insight(contex)
        return contex.stored
    }
}
