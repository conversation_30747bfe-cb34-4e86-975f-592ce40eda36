package no.ruter.tranop.app.event.journey.output

import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeTestChannel
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeTestClientFactory
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeTestConfig
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.outbox.InternalOutbox
import no.ruter.tranop.app.outbox.Outbox
import no.ruter.tranop.app.outbox.OutboxService
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.OutboxRecord
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class JourneyEventOutboxScheduleTest {
    private val timeService = mock<TimeService> {}
    private val outboxService = mock<OutboxService> {}
    private val clientFactory = SnowflakeTestClientFactory()
    private val streamingIngestClient =
        JourneyEventSnowflakeIngestClient(
            config = SnowflakeTestConfig.DEFAULT_CLIENT_PROPERTIES,
            clientFactory = clientFactory,
        )
    private val config =
        JourneyEventOutboxStreamingConfig().apply {
            batchSize = 10
            retryCount = 5
        }
    private val consumer =
        JourneyEventOutboxSchedule(
            timeService,
            outboxService,
            streamingIngestClient,
            config,
        )

    @BeforeEach
    fun setUp() {
        clientFactory.reset()
    }

    @Test
    fun `processPendingEvents marks published and failed events`() {
        val mockRecordRef1 = mock<OutboxRecord> { on { ref } doReturn "ref1" }
        val mockRecordRef2 = mock<OutboxRecord> { on { ref } doReturn "ref2" }
        val mockData = mock<Outbox>()
        val unpublished =
            listOf(
                mock<InternalOutbox> {
                    on { record } doReturn mockRecordRef1
                    on { data } doReturn mockData
                },
                mock<InternalOutbox> {
                    on { record } doReturn mockRecordRef2
                    on { data } doReturn mockData
                },
            )

        whenever(
            outboxService.findUnpublished(any(), any(), any(), any()),
        ).doReturn(unpublished)
        journeyEventIngestChannel().setRowErrors(rowIndexes = listOf(1)) // Fail second row on ingest.

        consumer.processPendingEvents()

        verify(outboxService).markAsPublished(listOf("ref1"))
        verify(outboxService).markAsFailed(listOf(unpublished[1]))
    }

    @Test
    fun `processPendingEvents handles ingest exceptions gracefully`() {
        val mockBox =
            InternalOutbox(
                data =
                    Outbox(
                        dataType = DBOutboxDataType.DATED_JOURNEY_EVENT,
                        targetType = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                        payload = "{}",
                        payloadRef = "payloadRef",
                    ),
                record = OutboxRecord(),
            )
        whenever(
            outboxService.findUnpublished(any(), any(), any(), any()),
        ).doReturn(listOf(mockBox))

        journeyEventIngestChannel().setRowExceptions(mapOf(0 to RuntimeException("ingest failed")))

        consumer.processPendingEvents()

        verify(outboxService).markAsFailed(any())
    }

    private fun journeyEventIngestChannel(): SnowflakeTestChannel =
        clientFactory.channel(
            clientName = JourneyEventSnowflakeIngestClient.CLIENT_BUILDER_NAME,
            channelName = JourneyEventSnowflakeIngestClient.CHANNEL_BUILDER_NAME,
        )
}
