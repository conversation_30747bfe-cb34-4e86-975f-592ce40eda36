package no.ruter.tranop.app.variance.deviation.api.reason

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.TraceInfoContext
import no.ruter.tranop.app.test.TestUtils
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode.BYPASS
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode.DELAY
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode.NO_SERVICE
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode.UNKNOWN
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever

class ReasonCodeServiceTest {
    private val reasonCodes = TestUtils.readReasonCodes("reason-codes/reason-codes.json").toReasonCodeList()
    private val xOperatorId =
        object : TraceInfoContext { // TODO: Find a better way to mock this.
            override val trace: TraceInfo =
                object : TraceInfo {
                    override val traceId: String = "trace-id"
                    override val requestId: String = "request-id"
                    override val operatorId: String = "xOperatorId"
                    override val authorityId: String = "xAuthorityId"
                }

            override fun resolveActiveOperator() { /* Nothing to do there */ }

            override val channel: DataChannel = RecordType.SERVICE_DEVIATION.channels.input
            override val metrics: Map<String, List<String>> = emptyMap()
        }

    private val trafficPortalServiceMock: TrafficPortalService = mock()
    private val reasonCodeService = ReasonCodeService(trafficPortalServiceMock)

    @Test
    fun `When ServiceDeviationCode Is Null Then return Reason Codes`() {
        whenever(trafficPortalServiceMock.getReasonCodes(any())).thenReturn(reasonCodes)

        val response = reasonCodeService.getReasonCodes(xOperatorId, null)

        assertEquals(58, response.reasons?.size)
        assertEquals(10, response.groups?.size)
        val reasonCode = response.reasons!!.first { it.code == "TRAFFIC_CONGESTION" }
        assertEquals("TRAFFIC_CONGESTION", reasonCode.code)
        assertEquals("Rushtid / kø", reasonCode.title)
        assertEquals(null, reasonCode.description)
        assertEquals(listOf(NO_SERVICE, DELAY), reasonCode.validDeviationCodes)
        assertEquals("58549d7cab230ffe6a976da6140b6bf5", reasonCode.groupCode)
        assertEquals("Trafikk og vær", response.groups?.find({ it.code == reasonCode.groupCode })?.title)
        assertEquals(null, response.groups?.find({ it.code == reasonCode.groupCode })?.description)
    }

    @Test
    fun `When ServiceDeviationCode is CANCELLATION Then return subset`() {
        whenever(trafficPortalServiceMock.getReasonCodes(any())).thenReturn(reasonCodes)

        val response = reasonCodeService.getReasonCodes(xOperatorId, NO_SERVICE)

        assertEquals(33, response.reasons!!.size)
        assertEquals(10, response.groups?.size)
        assertTrue(response.reasons!!.all { it.validDeviationCodes!!.contains(NO_SERVICE) })
    }

    @Test
    fun `When ServiceDeviationCode is DELAY Then return subset`() {
        whenever(trafficPortalServiceMock.getReasonCodes(any())).thenReturn(reasonCodes)

        val response = reasonCodeService.getReasonCodes(xOperatorId, DELAY)

        assertEquals(31, response.reasons!!.size)
        assertEquals(10, response.groups?.size)
        assertTrue(response.reasons!!.all { it.validDeviationCodes!!.contains(DELAY) })
    }

    @Test
    fun `When ServiceDeviationCode is BYPASS Then return subset`() {
        whenever(trafficPortalServiceMock.getReasonCodes(any())).thenReturn(reasonCodes)

        val response = reasonCodeService.getReasonCodes(xOperatorId, BYPASS)

        assertEquals(2, response.reasons!!.size)
        assertEquals(10, response.groups?.size)
        assertTrue(response.reasons!!.all { it.validDeviationCodes!!.contains(BYPASS) })
    }

    @Test
    fun `When ServiceDeviationCode gives no match Then return EmptyResponse`() {
        whenever(trafficPortalServiceMock.getReasonCodes(any())).thenReturn(reasonCodes)

        val response = reasonCodeService.getReasonCodes(xOperatorId, UNKNOWN)

        assertEquals(25, response.reasons!!.size)
        assertEquals(10, response.groups?.size)
        assertTrue(response.reasons!!.all { it.validDeviationCodes!!.contains(UNKNOWN) })
    }

    @Test
    fun `When invalid category then valid deviation code is UNKNOWN`() {
        whenever(
            trafficPortalServiceMock.getReasonCodes(any()),
        ).thenReturn(
            listOf(
                ReasonCode(
                    code = "INVALID_CODE",
                    display = "Invalid Code",
                    displayGroupHeading = "Invalid Group",
                    reasonCategory = "INVALID_CATEGORY",
                    reasonSubcategory = "INVALID_SUBCATEGORY",
                    requiresDescription = false,
                    applicableEventCategorizations =
                        listOf(
                            EventCategorization("INVALID_CATEGORY", "INVALID_SUBCATEGORY", listOf("INVALID_DIMENSION")),
                        ),
                ),
            ),
        )

        val res = reasonCodeService.getReasonCodes(xOperatorId, null)
        assertEquals(1, res.reasons?.size)
        assertEquals("UNKNOWN", res.reasons!![0].validDeviationCodes!![0].toString())
    }
}
