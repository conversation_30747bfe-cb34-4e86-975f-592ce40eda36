package no.ruter.tranop.app.variance.deviation.api

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.variance.common.AbstractADTv4ApiTest
import no.ruter.tranop.app.variance.common.test.ImpactUtils
import no.ruter.tranop.app.variance.deviation.db.ServiceDeviationRepository
import no.ruter.tranop.assignment.adt.v4.api.DeviationApi
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationGetResponse
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationListResponse
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationParameters
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationPostResponse
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationReasonCodeListResponse
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationSpec
import no.ruter.tranop.assignment.adt.v4.model.value.APIPostRequestType
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseCode
import org.junit.jupiter.api.Assertions
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import kotlin.reflect.KCallable
import kotlin.test.assertNotNull

abstract class AbstractADTv4DeviationApiTest : AbstractADTv4ApiTest() {
    companion object {
        const val ID_PARAM = "{serviceDeviationId}"
        const val SERVICE_DEVIATION_CODE = "{serviceDeviationCode}"
    }

    @Autowired
    protected lateinit var deviationRepo: ServiceDeviationRepository

    // ${api.base-path:/actual/base/path}
    protected val apiBasePath: String by lazy {
        val path = getRequestMappingValue(DeviationApi::class.annotations)
        path.split(":").last().removeSuffix("}")
    }

    protected fun resolveURL(callable: KCallable<*>): String {
        val path = getRequestMappingValue(callable.annotations)
        return "${apiBasePath}$path"
    }

    protected fun postDeviationRequest(
        body: APIServiceDeviationPostRequest,
        status: HttpStatus = HttpStatus.CREATED,
    ): APIServiceDeviationPostResponse {
        val url = resolveURL(DeviationApi::postServiceDeviation)
        val json = post(url, body, status, headers = HEADERS_ADT_AUTH)
        return JsonUtils.toObject(json, APIServiceDeviationPostResponse::class.java)
    }

    protected fun updateDeviationRequest(
        id: String,
        body: APIServiceDeviationPostRequest,
        status: HttpStatus = HttpStatus.OK,
        headers: Map<String, String> = HEADERS_ADT_AUTH,
    ): APIServiceDeviationPostResponse {
        val url = resolveURL(DeviationApi::updateServiceDeviation).replace(ID_PARAM, id)
        val json = post(url, body, status, headers)
        return JsonUtils.toObject(json, APIServiceDeviationPostResponse::class.java)
    }

    protected fun postServiceDeviationDeleteRequest(
        ref: String,
        comment: String,
        status: HttpStatus = HttpStatus.OK,
        headers: Map<String, String> = HEADERS_ADT_AUTH,
    ): APIServiceDeviationPostResponse {
        val body =
            APIServiceDeviationPostRequest(
                action = APIPostRequestType.DELETE.value,
                comment = comment,
            )
        return updateDeviationRequest(id = ref, body, status = status)
    }

    protected fun getReasonCodes(
        httpHeaders: Map<String, String>,
        serviceDeviationCode: APIServiceDeviationCode,
        status: HttpStatus = HttpStatus.OK,
    ): APIServiceDeviationReasonCodeListResponse {
        val url =
            resolveURL(DeviationApi::getServiceDeviationReasonCodes)
                .replace(SERVICE_DEVIATION_CODE, serviceDeviationCode.value)
        val json =
            get(
                url = url,
                status = status,
                headers = httpHeaders,
            )
        return JsonUtils.toObject(json, APIServiceDeviationReasonCodeListResponse::class.java)
    }

    protected fun getServiceDeviation(
        ref: String,
        status: HttpStatus = HttpStatus.OK,
        headers: Map<String, String> = HEADERS_ADT_AUTH,
    ): APIServiceDeviationGetResponse {
        val url = resolveURL(DeviationApi::getServiceDeviation).replace(ID_PARAM, ref)
        return getAndMap(
            url = url,
            headers = headers,
            status = status,
            responseClass = APIServiceDeviationGetResponse::class.java,
        )
    }

    protected fun findServiceDeviations(headers: Map<String, String> = HEADERS_ADT_AUTH): APIServiceDeviationListResponse {
        val url = resolveURL(DeviationApi::findServiceDeviations)
        return getAndMap(
            url = url,
            headers = headers,
            status = HttpStatus.OK,
            responseClass = APIServiceDeviationListResponse::class.java,
        )
    }

    protected fun registerDelayDeviation(delay: Int): String {
        val body = createDelayRequest(delay)
        val res = postDeviationRequest(body, status = HttpStatus.CREATED)
        val ref = assertNotNull(res.deviation!!.lifecycle!!.serviceDeviationId)
        val stored = assertNotNull(deviationRepo.fetchByRef(ref, restricted = false))

        val record = stored.record
        Assertions.assertEquals(ref, record.ref)
        Assertions.assertEquals(APIStatusResponseCode.OK.value, res.result!!.status!!.code)
        Assertions.assertEquals(OP_ID_OPERATOR_1, record.operatorId)
        Assertions.assertEquals(AUTH_ID_AUTHORITY_1, record.authorityId)
        return ref
    }

    protected fun createDelayRequest(delay: Int): APIServiceDeviationPostRequest {
        val spec =
            APIServiceDeviationSpec(
                code = APIServiceDeviationCode.DELAY.value,
                impact = ImpactUtils.journeyImpact(journeyId = "djj-something"),
                parameters = APIServiceDeviationParameters(delayMinutes = delay),
            )
        return APIServiceDeviationPostRequest(spec = spec)
    }
}
