package no.ruter.tranop.app.variance.deviation.api

import no.ruter.tranop.assignment.adt.v4.api.DeviationApi
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationGetResponse
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationListResponse
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationSpec
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseCode
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseReasonCode
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import kotlin.test.assertNotNull
import kotlin.time.Duration.Companion.days

class ADTv4DeviationAPITest : AbstractADTv4DeviationApiTest() {
    @Test
    fun `deviation - get - deviation not found`() {
        getAndMap(
            url = resolveURL(DeviationApi::getServiceDeviation).replace(ID_PARAM, "1337"),
            headers = HEADERS_ADT_AUTH,
            status = HttpStatus.NOT_FOUND,
            responseClass = APIServiceDeviationGetResponse::class.java,
        )
    }

    @Test
    fun `deviation - get - only deviations within date span`() {
        Assertions.assertEquals(0, deviationRepo.count())
        timeService.offset(duration = (-3).days)
        registerDelayDeviation(37)

        timeService.reset()
        val now = timeService.now()

        registerDelayDeviation(38)

        val response =
            getAndMap(
                url = resolveURL(DeviationApi::findServiceDeviations),
                headers = HEADERS_ADT_AUTH,
                status = HttpStatus.OK,
                responseClass = APIServiceDeviationListResponse::class.java,
                requestBuilder = {
                    queryParam("fromDateTime", now.minusDays(1).toString())
                    queryParam("toDateTime", now.plusDays(1).toString())
                },
            )

        assertNotNull(response.items) { deviations ->
            assertThat(deviations).hasSize(1)
            assertThat(
                deviations
                    .first()
                    .spec
                    ?.parameters
                    ?.delayMinutes,
            ).isEqualTo(38)
        }
    }

    @Test
    fun `deviation - post - invalid deviation code`() {
        val code = "invalid-code"
        val body = APIServiceDeviationPostRequest(spec = APIServiceDeviationSpec(code = code))
        val res = postDeviationRequest(body, status = HttpStatus.BAD_REQUEST)
        assertNotNull(res.result) { result ->
            Assertions.assertEquals(APIStatusResponseCode.ERR_CLIENT.value, result.status!!.code)
            assertNotNull(result.details) { details ->
                Assertions.assertEquals(2, details.size)
                assertNotNull(details.firstOrNull()) { detail ->
                    Assertions.assertNull(detail.code)
                    Assertions.assertEquals(APIStatusResponseReasonCode.DEVIATION_CODE_ERROR.value, detail.reason)
                    Assertions.assertTrue(detail.description!!.startsWith(prefix = "invalid or unsupported value [$code]:"))
                }
            }
        }
    }

    @Test
    fun `deviation - delete - delete deviation`() {
        val res = postServiceDeviationDeleteRequest(ref = "1337", comment = "no comment", status = HttpStatus.NOT_FOUND)
        Assertions.assertEquals(APIStatusResponseCode.ERR_CLIENT.value, res.result!!.status!!.code)
    }

    @Test
    fun `deviation - reason codes - get reason codes`() {
        val op = "xOperatorId"
        val res =
            getReasonCodes(
                mapOf(
                    HEADER_ADT_AUTH to adtAuthValue(operatorIds = listOf(op), authorityIds = listOf("whatever")),
                    HEADER_OPERATOR_ID to op,
                ),
                serviceDeviationCode = APIServiceDeviationCode.NO_SERVICE,
            )
        Assertions.assertEquals(APIStatusResponseCode.OK.value, res.result!!.status!!.code)
        Assertions.assertEquals(2, res.reasons!!.size)
        Assertions.assertEquals(2, res.groups!!.size)
    }

    @Test
    fun `deviation - reason codes - no X-Operator-Id gives forbidden`() {
        val res = getReasonCodes(emptyMap(), serviceDeviationCode = APIServiceDeviationCode.NO_SERVICE, status = HttpStatus.FORBIDDEN)
        Assertions.assertEquals(APIStatusResponseCode.ERR_CLIENT.value, res.result!!.status!!.code)
    }

    @Test
    fun `deviation - reason codes - invalid reason codes from traffic-portal-api Id gives deviation code of unknown`() {
        val op = "RUT:Operator:InvalidReasonCodes"
        val res =
            getReasonCodes(
                mapOf(
                    HEADER_ADT_AUTH to
                        adtAuthValue(
                            operatorIds = listOf(op),
                            authorityIds = listOf("the-absolute-authority"),
                        ),
                    HEADER_OPERATOR_ID to op,
                ),
                serviceDeviationCode = APIServiceDeviationCode.NO_SERVICE,
            )
        Assertions.assertEquals(APIStatusResponseCode.OK.value, res.result!!.status!!.code)
        Assertions.assertEquals(
            "UNKNOWN",
            res.reasons!![0].validDeviationCodes!![0],
        )
    }
}
