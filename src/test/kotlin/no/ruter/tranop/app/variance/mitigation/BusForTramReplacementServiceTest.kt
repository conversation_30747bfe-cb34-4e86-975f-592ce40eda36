package no.ruter.tranop.app.variance.mitigation

import no.ruter.avro.entity.datedjourney.v2.DatedJourneyKeyV2
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.tranop.app.test.TestUtils
import no.ruter.tranop.app.test.assertSize
import no.ruter.tranop.app.variance.mitigation.replacement.tram.Line12TramReplacement
import no.ruter.tranop.app.variance.mitigation.replacement.tram.Line13TramReplacement
import no.ruter.tranop.app.variance.mitigation.replacement.tram.Line15TramReplacement
import no.ruter.tranop.app.variance.mitigation.replacement.tram.Line17TramReplacement
import no.ruter.tranop.app.variance.mitigation.replacement.tram.Line18TramReplacement
import no.ruter.tranop.app.variance.mitigation.replacement.tram.TramLineReplacement
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTODatedJourneyDirectionCode
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.dated.journey.dto.model.common.DTOJourneyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOTransportMode
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class BusForTramReplacementServiceTest : AbstractADTv4MitigationApiTest() {
    @BeforeEach
    fun setUp() {
        timeService.reset()
        testEnvironment.reset()
    }

    @Test
    fun `mitigation - post - valid replacement service for line 12`() {
        val datedJourney = readDatedJourney("mitigation/service-replacement-line-12/dated-journey-01.json")
        adjustTime(datedJourney)

        val ref = datedJourney.ref
        datedJourneyInputService.process(ref, datedJourney)

        val res = registerReplacementServiceMitigation(ref)
        val replacements = res.mitigation?.replacements.assertSize(size = 1)
        val replacementsJourneys = replacements.first().replacements.assertSize(size = 1)
        val replacedJourneys = replacements.first().replaced.assertSize(size = 1)
        Assertions.assertNotEquals(replacedJourneys.first().spec?.journeyId, replacementsJourneys.first().spec?.journeyId)

        publishStopPointRoutine.execute(timeService.now().plus(publishStopPointRoutine.config.olderThan))
        publishDatedJourneyRoutine.execute(timeService.now())

        val records = kafka.datedJourneyProducer.getRecords().assertSize(size = 2)
        records.forEach { assertRequiredFieldsEntityDatedJourney(it.value()) }
        val extraJourneyRef = records.first { it.key() != ref }.key()

        val plannedJourney = assertJourneyExists(ref)
        val extraJourney = assertJourneyExists(extraJourneyRef)
        assertRequiredFieldsDatedJourney(plannedJourney)
        assertRequiredFieldsDatedJourney(extraJourney)
        validateStops(Line12TramReplacement(), DTODatedJourneyDirectionCode.OUTBOUND)

        Assertions.assertEquals(30, extraJourney.plan.calls.size)
        Assertions.assertEquals(extraJourney.operators.first(), extraJourney.operatorContracts.first().operator)
        Assertions.assertEquals(DTOEventType.CREATED, extraJourney.events.first().type)
        Assertions.assertNotNull(extraJourney.replaces)
        Assertions.assertEquals(
            ref,
            extraJourney.replaces.journeys
                .first()
                .entityDatedJourneyKeyV2Ref,
        )

        val extraInputJourney = assertJourneyInputExists(extraJourneyRef)
        Assertions.assertEquals(extraJourney.ref, extraInputJourney.ref)

        Assertions.assertEquals(
            plannedJourney.plan.firstDepartureDateTime.toOffsetDateTime(),
            extraJourney.plan.firstDepartureDateTime.toOffsetDateTime(),
        )
        Assertions.assertEquals(
            plannedJourney.plan.lastArrivalDateTime.toOffsetDateTime(),
            extraJourney.plan.lastArrivalDateTime.toOffsetDateTime(),
        )
        Assertions.assertEquals(
            extraJourney.plan.calls
                .first()
                .plannedDeparture
                .toOffsetDateTime(),
            extraJourney.plan.firstDepartureDateTime.toOffsetDateTime(),
        )
        Assertions.assertEquals(
            extraJourney.plan.calls
                .last()
                .plannedArrival
                .toOffsetDateTime(),
            extraJourney.plan.lastArrivalDateTime.toOffsetDateTime(),
        )
    }

    @Test
    fun `mitigation - post - valid replacement service for line 13`() {
        val datedJourney = readDatedJourney("mitigation/service-replacement-line-13/dated-journey-01.json")
        adjustTime(datedJourney)

        val ref = datedJourney.ref
        datedJourneyInputService.process(ref, datedJourney)

        val res = registerReplacementServiceMitigation(ref)
        val replacements = res.mitigation?.replacements.assertSize(size = 1)
        val replacementsJourneys = replacements.first().replacements.assertSize(size = 1)
        val replacedJourneys = replacements.first().replaced.assertSize(size = 1)
        Assertions.assertNotEquals(replacedJourneys.first().spec?.journeyId, replacementsJourneys.first().spec?.journeyId)

        publishStopPointRoutine.execute(timeService.now().plus(publishStopPointRoutine.config.olderThan))
        publishDatedJourneyRoutine.execute(timeService.now())

        val records = kafka.datedJourneyProducer.getRecords().assertSize(size = 2)
        records.forEach { assertRequiredFieldsEntityDatedJourney(it.value()) }
        val extraJourneyRef = records.first { it.key() != ref }.key()

        val plannedJourney = assertJourneyExists(ref)
        val extraJourney = assertJourneyExists(extraJourneyRef)
        assertRequiredFieldsDatedJourney(plannedJourney)
        assertRequiredFieldsDatedJourney(extraJourney)
        validateStops(Line13TramReplacement(), DTODatedJourneyDirectionCode.INBOUND)

        Assertions.assertTrue(extraJourney.extra)
        Assertions.assertEquals(22, extraJourney.plan.calls.size)
        Assertions.assertEquals(extraJourney.operators.first(), extraJourney.operatorContracts.first().operator)
        Assertions.assertEquals(DTOEventType.CREATED, extraJourney.events.first().type)
        Assertions.assertNotNull(extraJourney.replaces)
        Assertions.assertEquals(
            ref,
            extraJourney.replaces.journeys
                .first()
                .entityDatedJourneyKeyV2Ref,
        )

        val extraInputJourney = assertJourneyInputExists(extraJourneyRef)
        Assertions.assertEquals(extraJourney.ref, extraInputJourney.ref)

        Assertions.assertEquals(
            plannedJourney.plan.firstDepartureDateTime.toOffsetDateTime(),
            extraJourney.plan.firstDepartureDateTime.toOffsetDateTime(),
        )
        Assertions.assertEquals(
            plannedJourney.plan.lastArrivalDateTime.toOffsetDateTime(),
            extraJourney.plan.lastArrivalDateTime.toOffsetDateTime(),
        )
        Assertions.assertEquals(
            extraJourney.plan.calls
                .first()
                .plannedDeparture
                .toOffsetDateTime(),
            extraJourney.plan.firstDepartureDateTime.toOffsetDateTime(),
        )
        Assertions.assertEquals(
            extraJourney.plan.calls
                .last()
                .plannedArrival
                .toOffsetDateTime(),
            extraJourney.plan.lastArrivalDateTime.toOffsetDateTime(),
        )
    }

    @Test
    fun `mitigation - post - valid replacement service for line 15`() {
        val datedJourney = readDatedJourney("mitigation/service-replacement-line-15/dated-journey-01.json")
        adjustTime(datedJourney)

        val ref = datedJourney.ref
        datedJourneyInputService.process(ref, datedJourney)

        val res = registerReplacementServiceMitigation(ref)
        val replacements = res.mitigation?.replacements.assertSize(size = 1)
        val replacementsJourneys = replacements.first().replacements.assertSize(size = 1)
        val replacedJourneys = replacements.first().replaced.assertSize(size = 1)
        Assertions.assertNotEquals(replacedJourneys.first().spec?.journeyId, replacementsJourneys.first().spec?.journeyId)

        publishStopPointRoutine.execute(timeService.now().plus(publishStopPointRoutine.config.olderThan))
        publishDatedJourneyRoutine.execute(timeService.now())
        val records = kafka.datedJourneyProducer.getRecords().assertSize(size = 2)
        records.forEach { assertRequiredFieldsEntityDatedJourney(it.value()) }
        val extraJourneyRef = records.first { it.key() != ref }.key()

        val plannedJourney = assertJourneyExists(ref)
        val extraJourney = assertJourneyExists(extraJourneyRef)
        assertRequiredFieldsDatedJourney(plannedJourney)
        assertRequiredFieldsDatedJourney(extraJourney)
        validateStops(Line15TramReplacement(), DTODatedJourneyDirectionCode.OUTBOUND)

        Assertions.assertEquals(plannedJourney.plan.calls.size, extraJourney.plan.calls.size)
        Assertions.assertEquals(extraJourney.operators.first(), extraJourney.operatorContracts.first().operator)
        Assertions.assertEquals(DTOEventType.CREATED, extraJourney.events.first().type)
        Assertions.assertNotNull(extraJourney.replaces)
        Assertions.assertEquals(
            ref,
            extraJourney.replaces.journeys
                .first()
                .entityDatedJourneyKeyV2Ref,
        )

        val extraInputJourney = assertJourneyInputExists(extraJourneyRef)
        Assertions.assertEquals(extraJourney.ref, extraInputJourney.ref)

        Assertions.assertEquals(
            plannedJourney.plan.firstDepartureDateTime.toOffsetDateTime(),
            extraJourney.plan.firstDepartureDateTime.toOffsetDateTime(),
        )
        Assertions.assertEquals(
            plannedJourney.plan.lastArrivalDateTime.toOffsetDateTime(),
            extraJourney.plan.lastArrivalDateTime.toOffsetDateTime(),
        )
        Assertions.assertEquals(
            extraJourney.plan.calls
                .first()
                .plannedDeparture
                .toOffsetDateTime(),
            extraJourney.plan.firstDepartureDateTime.toOffsetDateTime(),
        )
        Assertions.assertEquals(
            extraJourney.plan.calls
                .last()
                .plannedArrival
                .toOffsetDateTime(),
            extraJourney.plan.lastArrivalDateTime.toOffsetDateTime(),
        )
    }

    @Test
    fun `mitigation - post - valid replacement service for line 17`() {
        val datedJourney = readDatedJourney("mitigation/service-replacement-line-17/dated-journey-01.json")
        adjustTime(datedJourney)

        val ref = datedJourney.ref
        datedJourneyInputService.process(ref, datedJourney)

        val res = registerReplacementServiceMitigation(ref)
        val replacements = res.mitigation?.replacements.assertSize(size = 1)
        val replacementsJourneys = replacements.first().replacements.assertSize(size = 1)
        val replacedJourneys = replacements.first().replaced.assertSize(size = 1)
        Assertions.assertNotEquals(replacedJourneys.first().spec?.journeyId, replacementsJourneys.first().spec?.journeyId)

        publishStopPointRoutine.execute(timeService.now().plus(publishStopPointRoutine.config.olderThan))
        publishDatedJourneyRoutine.execute(timeService.now())

        val records = kafka.datedJourneyProducer.getRecords().assertSize(size = 2)
        records.forEach { assertRequiredFieldsEntityDatedJourney(it.value()) }
        val extraJourneyRef = records.first { it.key() != ref }.key()

        val plannedJourney = assertJourneyExists(ref)
        val extraJourney = assertJourneyExists(extraJourneyRef)
        assertRequiredFieldsDatedJourney(plannedJourney)
        assertRequiredFieldsDatedJourney(extraJourney)
        validateStops(Line17TramReplacement(), DTODatedJourneyDirectionCode.OUTBOUND)

        Assertions.assertEquals(plannedJourney.plan.calls.size - 1, extraJourney.plan.calls.size) // Sinsenkrysset has no replacement call
        Assertions.assertEquals(extraJourney.operators.first(), extraJourney.operatorContracts.first().operator)
        Assertions.assertEquals(DTOEventType.CREATED, extraJourney.events.first().type)
        Assertions.assertNotNull(extraJourney.replaces)
        Assertions.assertEquals(
            ref,
            extraJourney.replaces.journeys
                .first()
                .entityDatedJourneyKeyV2Ref,
        )

        Assertions.assertNotNull(extraJourney.plan.firstDepartureDateTime.toOffsetDateTime())
        Assertions.assertNotNull(extraJourney.plan.lastArrivalDateTime.toOffsetDateTime())
        Assertions.assertEquals(
            extraJourney.plan.calls
                .first()
                .plannedDeparture
                .toOffsetDateTime(),
            extraJourney.plan.firstDepartureDateTime.toOffsetDateTime(),
        )
        Assertions.assertEquals(
            extraJourney.plan.calls
                .last()
                .plannedArrival
                .toOffsetDateTime(),
            extraJourney.plan.lastArrivalDateTime.toOffsetDateTime(),
        )
    }

    @Test
    fun `mitigation - post - valid replacement service for line 18`() {
        val datedJourney = readDatedJourney("mitigation/service-replacement-line-18/dated-journey-01.json")
        adjustTime(datedJourney)

        val ref = datedJourney.ref
        datedJourneyInputService.process(ref, datedJourney)

        val res = registerReplacementServiceMitigation(ref)
        val replacements = res.mitigation?.replacements.assertSize(size = 1)
        val replacementsJourneys = replacements.first().replacements.assertSize(size = 1)
        val replacedJourneys = replacements.first().replaced.assertSize(size = 1)
        Assertions.assertNotEquals(replacedJourneys.first().spec?.journeyId, replacementsJourneys.first().spec?.journeyId)

        publishStopPointRoutine.execute(timeService.now().plus(publishStopPointRoutine.config.olderThan))
        publishDatedJourneyRoutine.execute(timeService.now())

        val records = kafka.datedJourneyProducer.getRecords().assertSize(size = 2)
        records.forEach { assertRequiredFieldsEntityDatedJourney(it.value()) }
        val extraJourneyRef = records.first { it.key() != ref }.key()

        val plannedJourney = assertJourneyExists(ref)
        val extraJourney = assertJourneyExists(extraJourneyRef)
        assertRequiredFieldsDatedJourney(plannedJourney)
        assertRequiredFieldsDatedJourney(extraJourney)
        validateStops(Line18TramReplacement(), DTODatedJourneyDirectionCode.OUTBOUND)

        Assertions.assertEquals(plannedJourney.plan.calls.size - 1, extraJourney.plan.calls.size) // Sinsenkrysset has no replacement call
        Assertions.assertEquals(extraJourney.operators.first(), extraJourney.operatorContracts.first().operator)
        Assertions.assertEquals(DTOEventType.CREATED, extraJourney.events.first().type)
        Assertions.assertNotNull(extraJourney.replaces)
        Assertions.assertEquals(
            ref,
            extraJourney.replaces.journeys
                .first()
                .entityDatedJourneyKeyV2Ref,
        )

        Assertions.assertNotNull(extraJourney.plan.firstDepartureDateTime.toOffsetDateTime())
        Assertions.assertNotNull(extraJourney.plan.lastArrivalDateTime.toOffsetDateTime())
        Assertions.assertEquals(
            extraJourney.plan.calls
                .first()
                .plannedDeparture
                .toOffsetDateTime(),
            extraJourney.plan.firstDepartureDateTime.toOffsetDateTime(),
        )
        Assertions.assertEquals(
            extraJourney.plan.calls
                .last()
                .plannedArrival
                .toOffsetDateTime(),
            extraJourney.plan.lastArrivalDateTime.toOffsetDateTime(),
        )
    }

    private fun assertRequiredFieldsDatedJourney(journey: DTODatedJourney) {
        Assertions.assertNotNull(journey.ref)
        Assertions.assertNotNull(journey.header)
        Assertions.assertNotNull(journey.header.traceId)
        Assertions.assertNotNull(journey.header.publisherId)
        Assertions.assertNotNull(journey.header.receivedTimestamp)
        Assertions.assertEquals(if (journey.extra == true) 1 else 2, journey.events.size)
        Assertions.assertEquals(journey.extra == null, journey.cancelled == true)
        Assertions.assertFalse(journey.deleted)
        Assertions.assertFalse(journey.omitted)
        Assertions.assertEquals(DTOJourneyType.SERVICE_JOURNEY, journey.type)
        Assertions.assertNotNull(journey.vehicleTask)
        Assertions.assertNotNull(journey.journeyReferences.vehicleJourneyId)
        // Until a journeypattern is created or assigned
        Assertions.assertEquals(journey.journeyReferences.runtimePatternRef == null, journey.extra == true)
        Assertions.assertEquals(journey.journeyReferences.journeyPatternRef == null, journey.extra == true)
        Assertions.assertNotNull(journey.journeyReferences.externalJourneyRef)
        Assertions.assertNotNull(journey.journeyReferences.externalJourneyRefV2)
        Assertions.assertNotNull(journey.journeyReferences.datedServiceJourneyId)
        Assertions.assertNotNull(journey.operators.first().operatorRef)
        Assertions.assertNotNull(journey.operatorContracts.first().operator)
        Assertions.assertNotNull(journey.operatingDate)
        Assertions.assertNotNull(journey.plan.firstDepartureDateTime)
        Assertions.assertNotNull(journey.plan.lastArrivalDateTime)
        Assertions.assertNotNull(journey.plan.calls)
        Assertions.assertNotNull(journey.plan.stops)
        Assertions.assertNotNull(journey.plan.links)
        Assertions.assertNotNull(journey.direction)
        Assertions.assertNotNull(journey.order)
        Assertions.assertEquals(if (journey.extra == true) DTOTransportMode.BUS else DTOTransportMode.TRAM, journey.line.transportMode)
        Assertions.assertNotNull(journey.line.publicCode)
        Assertions.assertNotNull(journey.line.lineRef)
        Assertions.assertNotNull(journey.line.name)
        Assertions.assertNotNull(journey.line.backgroundColour)
        Assertions.assertNotNull(journey.line.textColour)
    }

    private fun assertRequiredFieldsEntityDatedJourney(datedJourneyKey: DatedJourneyKeyV2) {
        Assertions.assertNotNull(datedJourneyKey.entityHeader)
        Assertions.assertNotNull(datedJourneyKey.entityData)
        val journey = datedJourneyKey.entityData.journey
        Assertions.assertNotNull(journey.entityDatedJourneyKeyV2Ref)
        Assertions.assertNotNull(journey.name)
        Assertions.assertNotNull(journey.cancelled)
        Assertions.assertFalse(journey.omitted)
        Assertions.assertFalse(journey.deleted)
        Assertions.assertNotNull(journey.dataSource)
        Assertions.assertNotNull(journey.vehicleTaskId)
        Assertions.assertNotNull(journey.vehicleJourneyId)
        Assertions.assertEquals(journey.runtimePatternRef == null, journey.extra == true) // Until a journeypattern is created or assigned
        Assertions.assertEquals(journey.journeyPatternRef == null, journey.extra == true)
        Assertions.assertEquals("SERVICE_JOURNEY", journey.type)
        Assertions.assertTrue(journey.isPublic)
        Assertions.assertNotNull(journey.operators.first().operatorRef)
        Assertions.assertNotNull(
            journey.operatorContracts
                .first()
                .operator.operatorRef,
        )
        Assertions.assertNotNull(journey.callSequence)
        Assertions.assertNotNull(journey.stopPoints)
        Assertions.assertNotNull(journey.stopPointLinks)
        Assertions.assertNotNull(journey.firstDepartureDateTime)
        Assertions.assertNotNull(journey.lastArrivalDateTime)
        Assertions.assertNotNull(journey.operatingDate)
        Assertions.assertNotNull(journey.externalJourneyId)
        Assertions.assertNotNull(journey.externalDatedServiceJourneyId)
        Assertions.assertFalse(journey.deleted)
        Assertions.assertNotNull(journey.lifeCycleInfo.created)
        Assertions.assertNotNull(journey.journeyState.journeyMitigationState)
        Assertions.assertEquals(if (journey.extra == true) "BUS" else "TRAM", journey.line.transportMode)
        Assertions.assertNotNull(journey.line.publicCode)
        Assertions.assertNotNull(journey.line.lineRef)
        Assertions.assertNotNull(journey.line.name)
        Assertions.assertNotNull(journey.line.backgroundColour)
        Assertions.assertNotNull(journey.line.textColour)
    }

    private fun readDatedJourney(file: String): PDJDatedJourney = TestUtils.readDatedJourney(file)

    private fun validateStops(
        replacement: TramLineReplacement,
        directionCode: DTODatedJourneyDirectionCode,
    ) {
        val replacementStops = replacement.getReplacementStopCalls(directionCode).filterValues { it != null }.mapKeys { it.value?.quayRef }

        val stops = stopPointRepository.fetchAll().associateBy { it.stopPoint.quayRef }
        val records = kafka.stopPointProducer.getRecords().assertSize(size = stops.size)

        Assertions.assertEquals(replacementStops.size, stops.size)
        records.forEach {
            val record = it.value().entityData.stop
            Assertions.assertNotNull(record.externalStopPointId)
            val storedStop = stops[record.externalStopPointId]?.stopPoint ?: error("Stop point is null")

            Assertions.assertNotNull(storedStop.ref)
            Assertions.assertNotNull(storedStop.legacyQuayRef)
            Assertions.assertNotNull(storedStop.name)

            Assertions.assertEquals(storedStop.ref, it.key())
            Assertions.assertEquals(storedStop.legacyQuayRef, record.legacyStopPointId)
            Assertions.assertEquals(storedStop.name, record.name)
        }
    }
}
