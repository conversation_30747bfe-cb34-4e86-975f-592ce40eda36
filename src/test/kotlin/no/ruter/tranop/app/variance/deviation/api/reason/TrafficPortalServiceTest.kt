package no.ruter.tranop.app.variance.deviation.api.reason

import no.ruter.tranop.app.common.config.TrafficPortalApiProperties
import no.ruter.tranop.app.test.TestUtils
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.cache.CacheManager
import org.springframework.cache.concurrent.ConcurrentMapCacheManager

class TrafficPortalServiceTest {
    private lateinit var mockWebServer: MockWebServer
    private lateinit var trafficPortalService: TrafficPortalService
    private lateinit var cacheManager: CacheManager
    private lateinit var reasonCodesJson: String
    private val xOperatorId = "xOperatorId"

    @BeforeEach
    fun setUp() {
        mockWebServer = MockWebServer()
        mockWebServer.start()
        cacheManager = ConcurrentMapCacheManager("reasonCode")
        trafficPortalService =
            TrafficPortalService(
                TrafficPortalApiProperties().apply {
                    baseUrl = mockWebServer.url("/").toString()
                    xApiKey = "testApiKey"
                    readTimeoutSeconds = 5
                },
            )
        reasonCodesJson = TestUtils.readReasonCodes("reason-codes/reason-codes.json")
    }

    @AfterEach
    fun tearDown() {
        mockWebServer.shutdown()
    }

    @Test
    fun `when body is list of reason codes then return list of ReasonCodes`() {
        mockWebServer.enqueue(MockResponse().setBody(reasonCodesJson).setResponseCode(200))

        val reasonCodes = trafficPortalService.getReasonCodes(xOperatorId)

        Assertions.assertEquals(58, reasonCodes.size)
        val reasonCode = reasonCodes.first { it.code == "TRAFFIC_CONGESTION" }
        Assertions.assertEquals("TRAFFIC_CONGESTION", reasonCode.code)
        Assertions.assertEquals("Rushtid / kø", reasonCode.display)
        Assertions.assertEquals("Trafikk og vær", reasonCode.displayGroupHeading)
        Assertions.assertFalse(reasonCode.requiresDescription)
        Assertions.assertEquals("CANCELLATION", reasonCode.applicableEventCategorizations[0].category)
        Assertions.assertEquals("FULL", reasonCode.applicableEventCategorizations[0].subcategory)
        Assertions.assertEquals(2, reasonCode.applicableEventCategorizations[0].dimensions.size)
        Assertions.assertEquals("JOURNEY", reasonCode.applicableEventCategorizations[0].dimensions[0])
        Assertions.assertEquals("LINE", reasonCode.applicableEventCategorizations[0].dimensions[1])
    }

    @Test
    fun `when api returns 500 then throw exception`() {
        mockWebServer.enqueue(MockResponse().setResponseCode(500))

        Assertions.assertThrows(RuntimeException::class.java) {
            trafficPortalService.getReasonCodes(xOperatorId)
        }
    }

    @Test
    fun `when result body is is empty then throw exception`() {
        mockWebServer.enqueue(MockResponse().setBody("").setResponseCode(200))

        Assertions.assertThrows(RuntimeException::class.java) {
            trafficPortalService.getReasonCodes(xOperatorId)
        }
    }

    @Test
    fun `when response is set then return list of ReasonCode's`() {
        val jsonResponse =
            """
            [
                {"code": "RC1", "display": "Reason 1", "displayGroupHeading": "Group 1", "reasonCategory": "Reason Category 1", "reasonSubcategory": "Reason Subcategory 1", "requiresDescription": false, "applicableEventCategorizations": []},
                {"code": "RC2", "display": "Reason 2", "displayGroupHeading": "Group 2", "reasonCategory": "Reason Category 2", "reasonSubcategory": "Reason Subcategory 2", "requiresDescription": false, "applicableEventCategorizations": []}
            ]
            """.trimIndent()
        mockWebServer.enqueue(MockResponse().setBody(jsonResponse).setResponseCode(200))

        val reasonCodes = trafficPortalService.getReasonCodes(xOperatorId)

        Assertions.assertEquals(
            listOf(
                ReasonCode(
                    "RC1",
                    "Reason 1",
                    "Group 1",
                    "Reason Category 1",
                    "Reason Subcategory 1",
                    false,
                    emptyList(),
                ),
                ReasonCode(
                    "RC2",
                    "Reason 2",
                    "Group 2",
                    "Reason Category 2",
                    "Reason Subcategory 2",
                    false,
                    emptyList(),
                ),
            ),
            reasonCodes,
        )
    }

    @Test
    fun `when response is empty list then return empty list`() {
        val jsonResponse = "[]"
        mockWebServer.enqueue(MockResponse().setBody(jsonResponse).setResponseCode(200))

        val reasonCodes = trafficPortalService.getReasonCodes(xOperatorId)
        Assertions.assertEquals(emptyList<ReasonCode>(), reasonCodes)
    }
}
