package no.ruter.tranop.app.variance.deviation.api

import no.ruter.tranop.app.variance.common.api.adt.v4.input.ADTv4InputContext
import no.ruter.tranop.assignment.adt.v4.api.DeviationApi
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationGetResponse
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseCode
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import kotlin.test.assertNotNull

class ADTv4AuthHeaderTest : AbstractADTv4DeviationApiTest() {
    @Test
    fun `x-adt-auth - forbidden - missing x-adt-auth header`() {
        val res =
            getAndMap(
                url = url(),
                headers = emptyMap(),
                status = HttpStatus.FORBIDDEN,
                responseClass = APIServiceDeviationGetResponse::class.java,
            )

        val status = assertNotNull(assertNotNull(res.result).status)
        Assertions.assertEquals(APIStatusResponseCode.ERR_CLIENT.value, status.code)
        assertNotNull(status.description) { desc ->
            Assertions.assertTrue(desc.startsWith(prefix = ADTv4InputContext.MSG_AUTH_MISSING))
        }
    }

    @Test
    fun `x-adt-auth - forbidden - missing x-adt-auth operator id`() {
        val res =
            getAndMap(
                url = url(),
                headers = httpHeaders(authOperatorIds = null),
                status = HttpStatus.FORBIDDEN,
                responseClass = APIServiceDeviationGetResponse::class.java,
            )

        val status = assertNotNull(assertNotNull(res.result).status)
        Assertions.assertEquals(APIStatusResponseCode.ERR_CLIENT.value, status.code)
        assertNotNull(status.description) { desc ->
            Assertions.assertTrue(desc.startsWith(prefix = "${ADTv4InputContext.MSG_AUTH_MISSING}: operator not configured"))
        }
    }

    @Test
    fun `x-adt-auth - forbidden - missing x-adt-auth authority id`() {
        val res =
            getAndMap(
                url = url(),
                headers = httpHeaders(authAuthorityIds = null),
                status = HttpStatus.FORBIDDEN,
                responseClass = APIServiceDeviationGetResponse::class.java,
            )

        val status = assertNotNull(assertNotNull(res.result).status)
        Assertions.assertEquals(APIStatusResponseCode.ERR_CLIENT.value, status.code)
        assertNotNull(status.description) { desc ->
            Assertions.assertTrue(desc.startsWith(prefix = "${ADTv4InputContext.MSG_AUTH_MISSING}: authority not configured"))
        }
    }

    @Test
    fun `x-adt-auth - forbidden - operator id mismatch`() {
        val res =
            getAndMap(
                url = url(),
                headers = httpHeaders(requestOperatorId = "unlisted-operator-id"),
                status = HttpStatus.FORBIDDEN,
                responseClass = APIServiceDeviationGetResponse::class.java,
            )

        val status = assertNotNull(assertNotNull(res.result).status)
        Assertions.assertEquals(APIStatusResponseCode.ERR_CLIENT.value, status.code)
        assertNotNull(status.description) { desc ->
            Assertions.assertEquals("Restricted operator (no access)", desc)
        }
    }

    @Test
    fun `x-adt-auth - forbidden - authority id mismatch`() {
        val res =
            getAndMap(
                url = url(),
                headers = httpHeaders(requestAuthorityId = "unlisted-authority-id"),
                status = HttpStatus.FORBIDDEN,
                responseClass = APIServiceDeviationGetResponse::class.java,
            )

        val status = assertNotNull(assertNotNull(res.result).status)
        Assertions.assertEquals(APIStatusResponseCode.ERR_CLIENT.value, status.code)
        assertNotNull(status.description) { desc ->
            Assertions.assertEquals("Restricted authority (no access)", desc)
        }
    }

    private fun url() = resolveURL(DeviationApi::getServiceDeviation).replace(ID_PARAM, "1337")
}
