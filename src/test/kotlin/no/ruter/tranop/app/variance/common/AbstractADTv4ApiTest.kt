package no.ruter.tranop.app.variance.common

import no.ruter.tranop.app.test.AbstractApiTest
import no.ruter.tranop.common.dto.base64Encode
import no.ruter.tranop.common.dto.model.DTOAuthenticationMessage

abstract class AbstractADTv4ApiTest : AbstractApiTest() {
    companion object {
        const val OP_ID_OPERATOR_1 = "TEST:Operator:001"
        const val AUTH_ID_AUTHORITY_1 = "TEST:Authority:001"

        const val HEADER_ADT_AUTH = "X-ADT-Auth"
        const val HEADER_OPERATOR_ID = "X-Operator-Id"
        const val HEADER_AUTHORITY_ID = "X-Authority-Id"

        val HEADERS_ADT_AUTH = httpHeaders()

        fun httpHeaders(
            authOperatorIds: List<String>? = listOf(OP_ID_OPERATOR_1),
            authAuthorityIds: List<String>? = listOf(AUTH_ID_AUTHORITY_1),
            requestOperatorId: String? = null,
            requestAuthorityId: String? = null,
            includeAuth: Boolean = true,
            includeRequestOperatorId: Boolean = true,
            includeRequestAuthorityId: Boolean = true,
        ): Map<String, String> {
            val res = LinkedHashMap<String, String>(3)
            if (includeAuth) {
                res[HEADER_ADT_AUTH] = adtAuthValue(authOperatorIds, authAuthorityIds)
            }
            if (includeRequestOperatorId) {
                res[HEADER_OPERATOR_ID] = requestOperatorId ?: ""
            }
            if (includeRequestAuthorityId) {
                res[HEADER_AUTHORITY_ID] = requestAuthorityId ?: ""
            }
            return res
        }

        fun adtAuthValue(
            operatorIds: List<String>?,
            authorityIds: List<String>?,
        ) = DTOAuthenticationMessage()
            .apply {
                this.operatorIds = operatorIds
                this.authorityIds = authorityIds
            }.base64Encode()!!
    }
}
