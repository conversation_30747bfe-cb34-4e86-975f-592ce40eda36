package no.ruter.tranop.app.variance.mitigation

import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.tranop.app.plan.journey.JourneyData
import no.ruter.tranop.app.plan.journey.db.InternalDatedJourney
import no.ruter.tranop.app.plan.stop.input.StopPointInputContext
import no.ruter.tranop.app.variance.common.test.ImpactUtils
import no.ruter.tranop.assignment.adt.v4.model.APIServiceImpact
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationSpec
import no.ruter.tranop.assignment.adt.v4.model.value.APIPostRequestType
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseCode
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import no.ruter.tranop.journey.mitigation.dto.value.DTOServiceMitigationCode
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import kotlin.test.assertNotNull

// TODO: Add test for updating full cancellation
// TODO: Add test for deleting full cancellation
// TODO: Add test for updating partial cancellation
// TODO: Add test for deleting partial cancellation
class JourneyCancellationTest : AbstractADTv4MitigationApiTest() {
    val block = "1705-2023-01-25"
    val vehicleTask: List<PDJDatedJourney> = JourneyData.VEHICLE_TASKS[block]!!
    val vehicleTaskJourney03: PDJDatedJourney = vehicleTask[2]

    @BeforeEach
    fun setUp() {
        timeService.reset()
        testEnvironment.reset()
    }

    @Test
    fun `mitigation - cancellation - update not implemented`() {
        val serviceMitigationId = verifySingleJourneyCancellation()
        updateMitigationRequest(
            id = serviceMitigationId,
            body = APIServiceMitigationPostRequest(),
            status = HttpStatus.NOT_IMPLEMENTED,
        )
    }

    @Test
    fun `mitigation - cancellation - cancel single journey`() {
        verifySingleJourneyCancellation()
    }

    @Test
    fun `mitigation - cancellation - cancel single journey + delete cancellation`() {
        val ref = vehicleTaskJourney03.ref

        // TODO: Assert comment has been stored and is obtainable somewhere...
        val comment = "I changed my mind"
        val serviceMitigationId = verifySingleJourneyCancellation()

        postServiceMitigationDeleteRequest(serviceMitigationId, comment = comment)

        // Verify initial revision is not cancelled.
        val r3 = assertNotNull(journeyRepo.fetchByRef(ref))
        Assertions.assertEquals(3, r3.record.revision)
        assertCancellationStatus(r3, cancelled = false, partiallyCancelled = false)
    }

    private fun verifySingleJourneyCancellation(): String {
        storeDatedJourneys(vehicleTaskJourney03)
        val ref = vehicleTaskJourney03.ref

        // Verify initial revision is not cancelled.
        val r1 = assertNotNull(journeyRepo.fetchByRef(ref))
        Assertions.assertEquals(1, r1.record.revision)
        assertCancellationStatus(r1, cancelled = false, partiallyCancelled = false)

        val impact = ImpactUtils.journeyImpact(journeyId = ref)
        val body = createCancellationRequest(impact)

        val res = postMitigationRequest(body, status = HttpStatus.CREATED)
        Assertions.assertEquals(APIStatusResponseCode.OK.value, res.result!!.status!!.code)

        // Verify we now have a second revision, which is cancelled.
        val r2 = assertNotNull(journeyRepo.fetchByRef(ref))
        Assertions.assertEquals(2, r2.record.revision)
        assertCancellationStatus(r2, cancelled = true, partiallyCancelled = false)

        return assertNotNull(res.mitigation?.lifecycle?.serviceMitigationId)
    }

    @Test
    fun `mitigation - cancellation - cancel single call in single journey`() {
        storeDatedJourneys(vehicleTaskJourney03)
        val ref = vehicleTaskJourney03.ref

        // Verify initial revision is not cancelled.
        val r1 = assertNotNull(journeyRepo.fetchByRef(ref))
        Assertions.assertEquals(1, r1.record.revision)
        assertCancellationStatus(r1, cancelled = false, partiallyCancelled = false)

        val stopPointRef = "sp-58a583a2466513ca541af2927e0a9cf5"
        storeStopPoints(mapOf(stopPointRef to "NSR:Quay:11095"))

        val calls = mapOf(stopPointRef to "2023-01-25T07:28:00Z")
        val impact = ImpactUtils.journeyImpact(journeyId = ref, stopPointCalls = calls)
        val body = createCancellationRequest(impact)

        val res = postMitigationRequest(body, status = HttpStatus.CREATED)
        Assertions.assertEquals(APIStatusResponseCode.OK.value, res.result!!.status!!.code)

        // Verify we now have a second revision, which is cancelled.
        val r2 = assertNotNull(journeyRepo.fetchByRef(ref))
        Assertions.assertEquals(2, r2.record.revision)
        assertCancellationStatus(r2, cancelled = false, partiallyCancelled = true)
    }

    private fun assertCancellationStatus(
        record: InternalDatedJourney,
        cancelled: Boolean,
        partiallyCancelled: Boolean,
    ) {
        val journey = record.journey
        val journeyState = assertNotNull(journey.journeyState)
        val journeyMitigationState = assertNotNull(journeyState.journeyMitigationState)

        Assertions.assertEquals(cancelled, record.cancelled)
        Assertions.assertEquals(cancelled, journey.cancelled)
        Assertions.assertEquals(cancelled, journeyMitigationState.cancelled)

        Assertions.assertEquals(partiallyCancelled, record.partiallyCancelled)
        Assertions.assertEquals(partiallyCancelled, journeyMitigationState.partiallyCancelled)
    }

    private fun storeStopPoints(stopPoints: Map<String, String>) {
        stopPoints.entries.forEach { e ->
            val input = DTOStopPoint().withRef(e.key).withQuayRef(e.value)
            stopPointRepository.store(StopPointInputContext(stopPoint = input))
        }
    }

    private fun createCancellationRequest(
        impact: APIServiceImpact,
        action: APIPostRequestType = APIPostRequestType.CREATE,
    ): APIServiceMitigationPostRequest {
        val spec =
            APIServiceMitigationSpec(
                code = DTOServiceMitigationCode.CANCELLATION.value,
                impact = impact,
            )
        return APIServiceMitigationPostRequest(
            action = action.value,
            spec = spec,
        )
    }
}
