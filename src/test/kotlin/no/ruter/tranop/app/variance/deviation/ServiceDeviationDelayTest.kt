package no.ruter.tranop.app.variance.deviation

<<<<<<< HEAD:src/test/kotlin/no/ruter/tranop/app/variance/deviation/ServiceDeviationDelayTest.kt
import no.ruter.tranop.app.variance.deviation.api.AbstractADTv4DeviationApiTest
import no.ruter.tranop.app.variance.deviation.db.InternalServiceDeviation
=======
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.deviation.config.ServiceDeviationOutputConfig
import no.ruter.tranop.app.deviation.db.InternalServiceDeviation
import no.ruter.tranop.app.outbox.config.OutboxCleanupProperties
>>>>>>> ddb46286 (rework with anders comments):src/test/kotlin/no/ruter/tranop/app/deviation/ServiceDeviationDelayTest.kt
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviation
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationParameters
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseCode
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import org.springframework.test.context.TestPropertySource
import kotlin.test.assertNotNull

<<<<<<< HEAD:src/test/kotlin/no/ruter/tranop/app/variance/deviation/ServiceDeviationDelayTest.kt
class ServiceDeviationDelayTest : AbstractADTv4DeviationApiTest() {
=======
@TestPropertySource(
    properties = [
        "${OutboxCleanupProperties.CONF_PREFIX}.enabled=true",
        "${ServiceDeviationOutputConfig.KAFKA_AVRO_ENTITY_V1_ENABLED}=true",
        "${ServiceDeviationOutputConfig.KAFKA_DTO_V1_ENABLED}=true",
        "${ServiceDeviationOutputConfig.SNOWFLAKE_JSON_BI_V1_ENABLED}=true",
    ],
)
class ServiceDeviationDelayTest : AbstractDeviationApiTest() {
>>>>>>> ddb46286 (rework with anders comments):src/test/kotlin/no/ruter/tranop/app/deviation/ServiceDeviationDelayTest.kt
    companion object {
        private val ALT_HEADERS = httpHeaders(authOperatorIds = listOf("different-operator"))
    }

    @Test
    fun `deviation - delay - basic CRUD`() {
        // Create delay deviation via API
        val ref = registerDelayDeviation(delay = 3)

        // Assert deviation can be read via API
        val returned = getServiceDeviation(ref)
        assertDeviationDelay(ref, deviation = returned.deviation, delay = 3, revision = 1)

        // Assert deviation is _not_ readable by other operators.
        val restricted =
            getServiceDeviation(
                ref = ref,
                status = HttpStatus.NOT_FOUND,
                headers = ALT_HEADERS,
            )
        Assertions.assertNull(restricted.deviation)

        // Update deviation delay via API
        updateDelayDeviation(ref, delay = 7)

        // Assert deviation can be found via "search" API
        val itemResponse = findServiceDeviations()
        Assertions.assertEquals(1, assertNotNull(itemResponse.page).itemCount)
        assertNotNull(itemResponse.items).first().let { item ->
            assertNotNull(item.lifecycle) { life ->
                Assertions.assertEquals(ref, life.serviceDeviationId)
                assertDeviationDelay(ref, deviation = item, delay = 7, revision = 2)
            }
        }

        // Assert deviation has been added to outbox
        val deviation =
            outboxRepository.findUnpublished(
                dataType = DBOutboxDataType.SERVICE_DEVIATION,
                targetType = DBOutboxTargetType.KAFKA_JSON_INTERNAL,
                pagination = AbstractQueryBuilder.Pagination(10, 0),
            )

        Assertions.assertEquals(1, deviation.size)

        // Assert deviation can _not_ be found by other operators via "search" API
        val emptyResponse = findServiceDeviations(headers = ALT_HEADERS)
        Assertions.assertEquals(0, assertNotNull(emptyResponse.page).itemCount)

        // Assert deviation can be deleted via API
        val deleteResponse = postServiceDeviationDeleteRequest(ref, comment = "delete comment")
        Assertions.assertNull(deleteResponse.deviation)

        // Assert record has been correctly updated as deleted internally.
        val deleted = assertStoredDeviationDelay(ref, revision = 3, delay = 7)
        Assertions.assertTrue(deleted.deleted)
        Assertions.assertNotNull(deleted.record.deletedAt)
        assertNotNull(deleted.record.deletedRevision) { deletedRevision ->
            Assertions.assertEquals(3, deletedRevision)
        }

        // Assert deleted record can no longer be retrieved via API, even by its rightful owner.
        getServiceDeviation(ref, status = HttpStatus.NOT_FOUND)
    }

    private fun assertDeviationDelay(
        ref: String,
        deviation: APIServiceDeviation?,
        delay: Int,
        revision: Int,
    ) {
        assertNotNull(deviation) { d ->
            assertNotNull(d.spec) { s ->
                assertNotNull(s.parameters) { p ->
                    assertNotNull(p.delayMinutes) { m ->
                        Assertions.assertEquals(delay, m)
                    }
                }
            }
        }

        assertStoredDeviationDelay(ref, revision, delay)
    }

    private fun assertStoredDeviationDelay(
        ref: String,
        revision: Int,
        delay: Int,
    ): InternalServiceDeviation {
        val stored = assertNotNull(deviationRepo.fetchByRef(ref, restricted = false))
        Assertions.assertEquals(revision, stored.record.revision)
        assertNotNull(stored.data.spec) { s ->
            assertNotNull(s.parameters) { p ->
                assertNotNull(p.delayMinutes) { m ->
                    Assertions.assertEquals(delay, m)
                }
            }
        }
        return stored
    }

    private fun updateDelayDeviation(
        ref: String,
        delay: Int,
    ): String {
        val stored = getServiceDeviation(ref)
        val spec = stored.deviation!!.spec!!.copy(parameters = APIServiceDeviationParameters(delayMinutes = delay))
        val body = APIServiceDeviationPostRequest(spec = spec)
        val res = updateDeviationRequest(id = ref, body = body, status = HttpStatus.OK)
        val ref2 = assertNotNull(res.deviation!!.lifecycle!!.serviceDeviationId)
        Assertions.assertEquals(ref2, ref)

        val modified = assertNotNull(deviationRepo.fetchByRef(ref, restricted = false))
        Assertions.assertEquals(ref, modified.record.ref)
        Assertions.assertEquals(APIStatusResponseCode.OK.value, res.result!!.status!!.code)
        return ref
    }
}
