package no.ruter.tranop.app.variance.mitigation

import no.ruter.plandata.journey.dated.v2.dto.model.common.PDJLine
import no.ruter.plandata.journey.dated.v2.dto.value.PDJTransportMode
import no.ruter.tranop.app.plan.journey.input.JourneyInputUtils
import no.ruter.tranop.app.variance.common.test.ImpactUtils
import no.ruter.tranop.assignment.adt.v4.api.MitigationApi
import no.ruter.tranop.assignment.adt.v4.model.APIDateTimeRange
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationGetResponse
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationSpec
import no.ruter.tranop.assignment.adt.v4.model.value.APIPostRequestType
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceMitigationCode.REPLACEMENT_SERVICE
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseCode
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseReasonCode
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import kotlin.test.assertNotNull

class ADTv4MitigationAPITest : AbstractADTv4MitigationApiTest() {
    @Test
    fun `mitigation - get - mitigation not found`() {
        getAndMap(
            url = resolveURL(MitigationApi::getServiceMitigation).replace(ID_PARAM, "1337"),
            headers = HEADERS_ADT_AUTH,
            status = HttpStatus.NOT_FOUND,
            responseClass = APIServiceMitigationGetResponse::class.java,
        )
    }

    @Test
    fun `mitigation - post - invalid mitigation code`() {
        val code = "invalid-code"
        val body = APIServiceMitigationPostRequest(spec = APIServiceMitigationSpec(code = code))
        val res = postMitigationRequest(body, status = HttpStatus.BAD_REQUEST)
        assertNotNull(res.result) { result ->
            Assertions.assertEquals(APIStatusResponseCode.ERR_CLIENT.value, result.status!!.code)
            assertNotNull(result.details) { details ->
                Assertions.assertEquals(2, details.size)
                assertNotNull(details.firstOrNull()) { detail ->
                    Assertions.assertNull(detail.code)
                    Assertions.assertEquals(APIStatusResponseReasonCode.MITIGATION_CODE_ERROR.value, detail.reason)
                    Assertions.assertTrue(detail.description!!.startsWith(prefix = "invalid or unsupported value [$code]:"))
                }
            }
        }
    }

    @Test
    fun `mitigation - post - draft not implemented`() {
        val body =
            APIServiceMitigationPostRequest(
                spec = APIServiceMitigationSpec(code = REPLACEMENT_SERVICE.value),
                draft = true,
            )
        val res = postMitigationRequest(body, status = HttpStatus.NOT_IMPLEMENTED)
        Assertions.assertEquals(APIStatusResponseCode.ERR_SERVER.value, res.result!!.status!!.code)
    }

    @Test
    fun `mitigation - update - approve not implemented`() {
        val res = postServiceMitigationApproveRequest("sm-1337", status = HttpStatus.NOT_IMPLEMENTED)
        Assertions.assertEquals(APIStatusResponseCode.ERR_SERVER.value, res.result!!.status!!.code)
    }

    @Test
    fun `mitigation - delete - not found`() {
        val res = postServiceMitigationDeleteRequest(ref = "1337", comment = "no comment", status = HttpStatus.NOT_FOUND)
        Assertions.assertEquals(APIStatusResponseCode.ERR_CLIENT.value, res.result!!.status!!.code)
    }

    @Test
    fun `mitigation - post - valid replacement service`() {
        val now = timeService.now()
        val code = "REPLACEMENT_SERVICE"
        val ref = "djj-some-dated-journey-ref" // Note: must have djj- prefix to be valid as dated journey id
        val opDate = now.toLocalDate().toString()

        val serviceWindow = // Note: This is NOT a proper service window, but it overlaps with the actual service window, so it works...
            APIDateTimeRange(
                start = now.withHour(0).toString(), // start of day on operating date.
                end = now.plusDays(1).withHour(0).toString(), // start of next day, day after operating date.
            )
        val body =
            APIServiceMitigationPostRequest(
                action = APIPostRequestType.CREATE.value,
                spec =
                    APIServiceMitigationSpec(
                        code = code,
                        impact = ImpactUtils.journeyImpact(journeyId = ref, serviceWindow = serviceWindow),
                    ),
            )
        val journey1 =
            JourneyInputUtils
                .createDatedJourney(
                    ref = ref,
                    operatingDate = opDate,
                )
        adjustTime(journey1)
        journey1.direction = "1"
        journey1.line =
            PDJLine().apply {
                lineRef = "RUT:Line:12"
                transportMode = PDJTransportMode.TRAM
            }
        datedJourneyInputService.process(journey1.ref, journey1)

        val res = postMitigationRequest(body, status = HttpStatus.CREATED)
        Assertions.assertEquals(APIStatusResponseCode.OK.value, res.result!!.status!!.code)
    }
}
