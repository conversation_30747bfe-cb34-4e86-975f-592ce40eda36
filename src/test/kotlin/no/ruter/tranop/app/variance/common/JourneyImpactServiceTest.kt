package no.ruter.tranop.app.variance.common

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.plan.journey.JourneyData
import no.ruter.tranop.app.plan.journey.input.AbstractDatedJourneyInputTest
import no.ruter.tranop.app.variance.common.api.adt.ADTAuthTrace
import no.ruter.tranop.app.variance.common.api.adt.v4.input.ADTv4InputContext
import no.ruter.tranop.app.variance.common.spec.target.JourneyTargets
import no.ruter.tranop.app.variance.common.test.ImpactUtils
import no.ruter.tranop.app.variance.deviation.api.input.DeviationInputMapper
import no.ruter.tranop.assignment.adt.v4.model.APIServiceImpact
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.springframework.beans.factory.annotation.Autowired
import java.time.OffsetDateTime
import kotlin.test.Test

class JourneyImpactServiceTest : AbstractDatedJourneyInputTest() {
    @Autowired
    lateinit var impactService: JourneyImpactService

    val block1 = "6006-2022-05-30"
    val vehicleTask = JourneyData.VEHICLE_TASKS[block1]!!

    lateinit var inputMapper: DeviationInputMapper

    val context =
        ADTv4InputContext(
            now = OffsetDateTime.now(),
            request = "nothing",
            requestTrace = ADTAuthTrace.NONE,
            channel = RecordType.SERVICE_DEVIATION.channels.internal,
        )

    @BeforeEach
    fun setUp() {
        inputMapper = DeviationInputMapper(timeService, insightService)

        timeService.reset()
        testEnvironment.reset()
    }

    @Test
    fun targetJourneysByLineRef() {
        val ref = "RUT:Line:60"
        val data = mapOf(block1 to vehicleTask)
        storeDatedJourneys(data)

        val journeyCount = journeyRepo.count()
        Assertions.assertEquals(vehicleTask.size, journeyCount)

        val impact = ImpactUtils.lineImpact(lineId = ref)
        val targets = findJourneyTargets(impact)
        val targetLineJourneys = targets.lineJourneys

        Assertions.assertEquals(0, targets.journeys.size)
        Assertions.assertEquals(1, targets.lineJourneys.size)
        Assertions.assertEquals(0, targets.stopPointJourneys.size)

        val line = targetLineJourneys.first()
        Assertions.assertEquals(journeyCount - 1, line.refs.size) // dead-run not included.
    }

    @Test
    fun targetJourneysByQuayRef() {
        val ref = "NSR:Quay:10947"
        val data = mapOf(block1 to vehicleTask)
        storeDatedJourneys(data)

        val journeyCount = journeyRepo.count()
        Assertions.assertEquals(vehicleTask.size, journeyCount)

        val impact = ImpactUtils.stopImpact(quayId = ref)
        val targets = findJourneyTargets(impact)
        val targetStopPointJourneys = targets.stopPointJourneys

        Assertions.assertEquals(0, targets.journeys.size)
        Assertions.assertEquals(0, targets.lineJourneys.size)
        Assertions.assertEquals(1, targets.stopPointJourneys.size)

        val stopPoint = targetStopPointJourneys.first()
        val stopPointJourneyRefs = stopPoint.refs
        Assertions.assertEquals(2, stopPointJourneyRefs.size) // Only journey 2 and 4 stop at this quay.
        Assertions.assertTrue(stopPointJourneyRefs.contains("jrbf68472641134fd285435ec37e2a5347"))
        Assertions.assertTrue(stopPointJourneyRefs.contains("jr65779f92b3df4e5f9c96238bcfc83602"))
    }

    @Test
    fun targetJourneyByVehicleJourneyId() {
        targetJourneyByJourneyId(journeyId = "367217")
    }

    @Test
    fun targetJourneyByExternalJourneyRefServiceJourneyId() {
        targetJourneyByJourneyId(journeyId = "RUT:ServiceJourney:60-165021-21465321")
    }

    private fun targetJourneyByJourneyId(journeyId: String) {
        val data = mapOf(block1 to vehicleTask)
        storeDatedJourneys(data)
        Assertions.assertEquals(vehicleTask.size, journeyRepo.count())

        val departures =
            listOf(
                "2022-05-30T04:47Z",
                "2022-05-30T04:47:00Z",
                "2022-05-30T04:47:11Z",
                "2022-05-30T04:47:13.37Z",
            )
        departures.forEach { departures ->
            val impact =
                ImpactUtils.journeyImpact(
                    lineId = "RUT:Line:60",
                    journeyId = journeyId,
                    firstDepartureDateTime = departures,
                )
            val targets = findJourneyTargets(impact)
            val targetJourneys = targets.journeys

            Assertions.assertEquals(1, targetJourneys.size)
            Assertions.assertEquals(0, targets.lineJourneys.size)
            Assertions.assertEquals(0, targets.stopPointJourneys.size)

            val journey = targetJourneys.first()
            Assertions.assertEquals("jrd689be5b32b2452188cb976cc40d3802", journey.ref)
        }
    }

    @Test
    fun targetJourneyByDatedJourneyV2Ref() {
        val ref = "jr5158ebc9d53c4acf90edf17413351344"
        val data = mapOf(block1 to vehicleTask)
        storeDatedJourneys(data)
        Assertions.assertEquals(vehicleTask.size, journeyRepo.count())

        val impact = ImpactUtils.journeyImpact(journeyId = ref)
        val targets = findJourneyTargets(impact)
        val targetJourneys = targets.journeys

        Assertions.assertEquals(1, targetJourneys.size)
        Assertions.assertEquals(0, targets.lineJourneys.size)
        Assertions.assertEquals(0, targets.stopPointJourneys.size)

        val journey = targetJourneys.first()
        Assertions.assertEquals(ref, journey.ref)
    }

    private fun findJourneyTargets(impact: APIServiceImpact): JourneyTargets {
        val res =
            impactService.findJourneyTargets(
                type = RecordType.SERVICE_DEVIATION,
                impact = inputMapper.mapImpact(impact, path = "$", context = context),
            )
        Assertions.assertTrue(res.context.valid) {
            "unexpected invalid impact context:\n${res.context.summary()}"
        }
        return res
    }
}
