package no.ruter.tranop.app.variance.common

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.common.dto.model.DTODateTimeRange
import org.junit.jupiter.api.Assertions
import java.time.OffsetDateTime
import kotlin.test.Test
import kotlin.test.assertNotNull

class ServiceImpactValidatorTest {
    private val type = RecordType.SERVICE_DEVIATION
    private val validator = ServiceImpactValidator()

    @Test
    fun `validate impact - duration - valid - positive duration`() {
        val now = OffsetDateTime.now()
        val range =
            DTODateTimeRange().apply {
                end = now.toString()
                start = now.minusDays(1).toString()
            }
        val res = validator.validateImpact(type, impact = null, duration = range)
        Assertions.assertFalse(res.valid)

        val errors = res.errors
        Assertions.assertEquals(1, errors.size) // missing impact is OK, we do not care.
        assertNotNull(res.duration) { duration ->
            Assertions.assertEquals(range.end, duration.end.toString())
            Assertions.assertEquals(range.start, duration.start.toString())
        }
    }

    @Test
    fun `validate impact - duration - invalid - zero duration`() {
        val now = OffsetDateTime.now()
        val time = now.toString()
        val range =
            DTODateTimeRange().apply {
                end = time
                start = time
            }
        val res = validator.validateImpact(type, impact = null, duration = range)
        Assertions.assertFalse(res.valid)

        val errors = res.errors
        Assertions.assertEquals(2, errors.size)

        val msg = $$"$.spec.duration : duration is zero: end time [$$time] is same as start time [$$time] : "
        Assertions.assertEquals(msg, errors.first().toString()) { res.summary() }
    }

    @Test
    fun `validate impact - duration - invalid - negative duration`() {
        val now = OffsetDateTime.now()
        val range =
            DTODateTimeRange().apply {
                end = now.toString()
                start = now.plusDays(1).toString()
            }
        val res = validator.validateImpact(type, impact = null, duration = range)
        Assertions.assertFalse(res.valid)

        val errors = res.errors
        Assertions.assertEquals(2, errors.size)

        val msg = $$"$.spec.duration : duration is negative: end time [$${range.end}] is before start time [$${range.start}] : "
        Assertions.assertEquals(msg, errors.first().toString()) { res.summary() }
    }
}
