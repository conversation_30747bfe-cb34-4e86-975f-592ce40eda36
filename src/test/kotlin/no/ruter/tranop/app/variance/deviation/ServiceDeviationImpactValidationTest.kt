package no.ruter.tranop.app.variance.deviation

import no.ruter.tranop.app.variance.deviation.api.AbstractADTv4DeviationApiTest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationSpec
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseCode
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseReasonCode
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import kotlin.test.assertNotNull

class ServiceDeviationImpactValidationTest : AbstractADTv4DeviationApiTest() {
    @Test
    fun `deviation api - post - bad request - missing impact`() {
        val code = APIServiceDeviationCode.NO_SERVICE.value
        val body = APIServiceDeviationPostRequest(spec = APIServiceDeviationSpec(code = code))
        val res = postDeviationRequest(body, status = HttpStatus.BAD_REQUEST)
        assertNotNull(res.result) { result ->
            Assertions.assertEquals(APIStatusResponseCode.ERR_CLIENT.value, result.status!!.code)
            assertNotNull(result.details) { details ->
                Assertions.assertEquals(1, details.size)
                assertNotNull(details.firstOrNull()) { detail ->
                    Assertions.assertNull(detail.code)
                    Assertions.assertEquals(APIStatusResponseReasonCode.BAD_REQUEST.value, detail.reason)
                    Assertions.assertTrue(
                        detail.description!!.contains("missing required service deviation impact"),
                    )
                }
            }
        }
    }
}
