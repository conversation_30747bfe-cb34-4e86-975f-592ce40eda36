package no.ruter.tranop.app.variance.common.test

import no.ruter.tranop.assignment.adt.v4.model.APIDateTimeRange
import no.ruter.tranop.assignment.adt.v4.model.APIJourneyCallSpec
import no.ruter.tranop.assignment.adt.v4.model.APIJourneyLineSpec
import no.ruter.tranop.assignment.adt.v4.model.APIJourneyLineSpecWindow
import no.ruter.tranop.assignment.adt.v4.model.APIJourneySpec
import no.ruter.tranop.assignment.adt.v4.model.APIJourneySpecWindow
import no.ruter.tranop.assignment.adt.v4.model.APIJourneySpecWindowOption
import no.ruter.tranop.assignment.adt.v4.model.APIServiceImpact
import no.ruter.tranop.assignment.adt.v4.model.APIStopPointSpec
import no.ruter.tranop.assignment.adt.v4.model.APIStopPointSpecWindow

class ImpactUtils private constructor() {
    companion object {
        /** Create a service impact with a single line and an optional service window. **/
        fun lineImpact(
            lineId: String?,
            lineDirection: String? = null,
            serviceWindow: APIDateTimeRange? = null,
        ): APIServiceImpact {
            val spec =
                APIJourneyLineSpec(
                    lineId = lineId,
                    direction = lineDirection,
                )
            val lineSpecWindow =
                APIJourneyLineSpecWindow(
                    spec = spec,
                    serviceWindow = serviceWindow,
                )
            return APIServiceImpact(lines = listOf(lineSpecWindow))
        }

        /** Create a service impact with a single stop point and an optional service window. **/
        fun stopImpact(
            quayId: String?,
            stopPointId: String? = null,
            serviceWindow: APIDateTimeRange? = null,
        ): APIServiceImpact {
            val spec =
                APIStopPointSpec(
                    quayId = quayId,
                    stopPointId = stopPointId,
                )
            val stopSpecWindow =
                APIStopPointSpecWindow(
                    spec = spec,
                    serviceWindow = serviceWindow,
                )
            return APIServiceImpact(stopPoints = listOf(stopSpecWindow))
        }

        /** Create a service impact with a single (dated) journey and an optional service window. **/
        fun journeyImpact(
            journeyId: String,
            lineId: String? = null,
            firstDepartureDateTime: String? = null,
            serviceWindow: APIDateTimeRange? = null,
            quayCalls: Map<String, String>? = null,
            stopPointCalls: Map<String, String>? = null,
        ): APIServiceImpact {
            val spec = APIJourneySpec(lineId = lineId, journeyId = journeyId, firstDepartureDateTime = firstDepartureDateTime)
            val journeySpecWindow =
                APIJourneySpecWindow(
                    spec = spec,
                    serviceWindow = serviceWindow,
                )

            val calls = ArrayList<APIJourneyCallSpec>()
            quayCalls?.entries?.forEach { e ->
                val stop = APIStopPointSpec(quayId = e.key)
                calls.add(APIJourneyCallSpec(stopPoint = stop, departureDateTime = e.value))
            }
            stopPointCalls?.entries?.forEach { e ->
                val stop = APIStopPointSpec(stopPointId = e.key)
                calls.add(APIJourneyCallSpec(stopPoint = stop, departureDateTime = e.value))
            }
            val option =
                APIJourneySpecWindowOption(
                    calls = if (calls.isEmpty()) null else calls,
                    journey = journeySpecWindow,
                )
            return APIServiceImpact(journeys = listOf(option))
        }
    }
}
