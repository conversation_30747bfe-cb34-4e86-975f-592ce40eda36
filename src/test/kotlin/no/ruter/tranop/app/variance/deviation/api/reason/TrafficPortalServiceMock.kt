package no.ruter.tranop.app.variance.deviation.api.reason

import no.ruter.tranop.app.common.config.TrafficPortalApiProperties

class TrafficPortalServiceMock : TrafficPortalService(configProperties = TrafficPortalApiProperties()) {
    override fun getReasonCodes(operatorRef: String): List<ReasonCode> =
        if (operatorRef == "RUT:Operator:InvalidReasonCodes") {
            listOf(
                ReasonCode(
                    "1",
                    "Display 1",
                    "Group 1",
                    "Reason Category",
                    "Reason Subcategory",
                    false,
                    listOf(EventCategorization("INVALID_CATEGORY", "INVALID_SUBCATEGORY", listOf("INVALID_DIMENSION"))),
                ),
                ReasonCode(
                    "2",
                    "Display 2",
                    "Group 2",
                    "Reason Category",
                    "Reason Subcategory",
                    true,
                    listOf(EventCategorization("INVALID_CATEGORY", "INVALID_SUBCATEGORY", listOf("INVALID_DIMENSION"))),
                ),
            )
        } else {
            listOf(
                ReasonCode(
                    "1",
                    "Display 1",
                    "Group 1",
                    "Reason Category",
                    "Reason Subcategory",
                    false,
                    emptyList(),
                ),
                ReasonCode(
                    "2",
                    "Display 2",
                    "Group 2",
                    "Reason Category",
                    "Reason Subcategory",
                    true,
                    emptyList(),
                ),
            )
        }
}
