package no.ruter.tranop.app.variance.common.spec

import org.junit.jupiter.api.Assertions
import kotlin.test.Test
import kotlin.test.assertNotNull

class JourneyIdTest {
    @Test
    fun shouldClassifyVehicleJourneyId() {
        val value = "207565"

        val journeyId = assertNotNull(JourneyId.parse(value))
        Assertions.assertEquals(value, journeyId.value)
        Assertions.assertEquals(value, journeyId.rawValue)
        Assertions.assertEquals(JourneyId.Type.VEHICLE_JOURNEY_ID, journeyId.type)
    }

    @Test
    fun shouldClassifyDatedJourneyId() {
        val values =
            listOf(
                "jr16e5a02a924b4304823b2daa9ec42bb7",
                "dj-ad18bdecd45d6c1819c19c0e8d5839ef",
                "djj-7d747b822327272735a149243646bf9b",
            )

        for (value in values) {
            val journeyId = assertNotNull(JourneyId.parse(value))
            Assertions.assertEquals(value, journeyId.value)
            Assertions.assertEquals(value, journeyId.rawValue)
            Assertions.assertEquals(JourneyId.Type.DATED_JOURNEY_ID, journeyId.type)
        }
    }

    @Test
    fun shouldClassifyServiceJourneyId() {
        val value = "RUT:ServiceJourney:002327-002794-113700"

        val journeyId = assertNotNull(JourneyId.parse(value))
        Assertions.assertEquals(value, journeyId.rawValue)
        Assertions.assertEquals("002327-002794-113700", journeyId.value)
        Assertions.assertEquals(JourneyId.Type.SERVICE_JOURNEY_ID, journeyId.type)
    }

    @Test
    fun shouldClassifyDatedServiceJourneyId() {
        val value = "RUT:DatedServiceJourneyId:jr41d415e9db0042caa316626393a4fe8b"

        val journeyId = assertNotNull(JourneyId.parse(value))
        Assertions.assertEquals(value, journeyId.rawValue)
        Assertions.assertEquals("jr41d415e9db0042caa316626393a4fe8b", journeyId.value)
        Assertions.assertEquals(JourneyId.Type.DATED_SERVICE_JOURNEY_ID, journeyId.type)
    }
}
