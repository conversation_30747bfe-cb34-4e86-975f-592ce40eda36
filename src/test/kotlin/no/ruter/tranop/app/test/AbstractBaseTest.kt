package no.ruter.tranop.app.test

import io.micrometer.core.instrument.Measurement
import io.micrometer.core.instrument.Meter
import io.zonky.test.db.AutoConfigureEmbeddedDatabase
import jakarta.annotation.PostConstruct
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.rdp.logging.Logger
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.Application
import no.ruter.tranop.app.common.dataflow.kafka.KafkaTestConfig
import no.ruter.tranop.app.common.dataflow.opensearch.OpenSearchNode
import no.ruter.tranop.app.common.dataflow.opensearch.OpenSearchProperties
import no.ruter.tranop.app.common.dataflow.opensearch.OpenSearchService
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TestTimeService
import no.ruter.tranop.app.event.journey.db.JourneyEventRepository
import no.ruter.tranop.app.event.journey.input.JourneyEventInputService
import no.ruter.tranop.app.event.traffic.config.TrafficEventConfig
import no.ruter.tranop.app.event.traffic.input.TrafficEventInputService
import no.ruter.tranop.app.outbox.db.OutboxRepository
import no.ruter.tranop.app.plan.journey.DatedJourneyService
import no.ruter.tranop.app.plan.journey.config.DatedJourneyConfigProperties
import no.ruter.tranop.app.plan.journey.db.DatedJourneyRepository
import no.ruter.tranop.app.plan.journey.db.InternalDatedJourney
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputService
import no.ruter.tranop.app.plan.journey.input.db.DatedJourneyInputData
import no.ruter.tranop.app.plan.journey.input.db.DatedJourneyInputRepository
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyLifeCycleConfig
import no.ruter.tranop.app.plan.journey.output.opensearch.DatedJourneyOpenSearchPublishRoutine
import no.ruter.tranop.app.plan.link.db.StopPointLinkRepository
import no.ruter.tranop.app.plan.link.input.StopPointLinkInputService
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.app.plan.stop.input.StopPointInputService
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestInstance
import org.opensearch.action.admin.indices.refresh.RefreshRequest
import org.opensearch.common.settings.Settings
import org.opensearch.node.InternalSettingsPreparer
import org.opensearch.node.Node
import org.opensearch.transport.Netty4Plugin
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.flyway.FlywayDataSource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import java.nio.file.Paths
import java.time.Duration
import kotlin.io.path.ExperimentalPathApi
import kotlin.io.path.Path
import kotlin.io.path.deleteRecursively
import kotlin.test.assertNotNull

@SpringBootTest(
    classes = [
        Application::class,
        TestConfig::class,
        KafkaTestConfig::class,
    ],
    properties = [
        "spring.main.allow-bean-definition-overriding=true",
        "spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration",
    ],
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@FlywayDataSource
@ActiveProfiles(value = ["test", "local"])
@AutoConfigureEmbeddedDatabase(
    type = AutoConfigureEmbeddedDatabase.DatabaseType.POSTGRES,
    provider = AutoConfigureEmbeddedDatabase.DatabaseProvider.ZONKY,
    refresh = AutoConfigureEmbeddedDatabase.RefreshMode.AFTER_EACH_TEST_METHOD,
)
abstract class AbstractBaseTest {
    protected val log: Logger = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var datedJourneyConfigProperties: DatedJourneyConfigProperties

    @Autowired
    lateinit var datedJourneyLifeCycleConfig: DatedJourneyLifeCycleConfig

    @Autowired
    lateinit var testEnvironment: TestEnvironment

    @Autowired
    lateinit var timeService: TestTimeService

    @Autowired
    lateinit var insightService: InsightService

    @Autowired
    lateinit var journeyRepo: DatedJourneyRepository

    @Autowired
    lateinit var journeyInputRepo: DatedJourneyInputRepository

    @Autowired
    lateinit var datedJourneyService: DatedJourneyService

    @Autowired
    lateinit var journeyEventRepository: JourneyEventRepository

    @Autowired
    lateinit var journeyEventService: JourneyEventInputService

    @Autowired
    lateinit var trafficEventService: TrafficEventInputService

    @Autowired
    lateinit var trafficEventConfig: TrafficEventConfig

    @Autowired
    lateinit var datedJourneyInputService: DatedJourneyInputService

    @Autowired
    lateinit var stopPointRepository: StopPointRepository

    @Autowired
    lateinit var stopPointLinkRepository: StopPointLinkRepository

    @Autowired
    lateinit var outboxRepository: OutboxRepository

    @Autowired
    lateinit var openSearchService: OpenSearchService

    @Autowired
    lateinit var openSearchProperties: OpenSearchProperties

    @Autowired
    lateinit var publishDatedJourneyToOpenSearchRoutine: DatedJourneyOpenSearchPublishRoutine

    @Autowired
    lateinit var stopPointLinkInputService: StopPointLinkInputService

    @Autowired
    lateinit var stopPointInputService: StopPointInputService

    val meterRegistry by lazy { insightService.metricsService.meterRegistry }

    @PostConstruct
    fun setUpConfig() {
        // Do something?
    }

    val metricsMap: List<Pair<Meter, MutableIterable<Measurement>>>
        get() = meterRegistry.meters.map { it to it.measure() }

    companion object {
        private const val BASE_DATA_PATH = "tmp/data"
        private const val NODE_NAME = "test-opensearch-node"
        private val CONF_PATH = Path("os_config.yaml")
        lateinit var openSearchSingleNode: Node

        @JvmStatic
        @BeforeAll
        fun setupOs() {
            val settings = getOsSettings()

            val env = InternalSettingsPreparer.prepareEnvironment(settings, emptyMap(), CONF_PATH) { NODE_NAME }
            openSearchSingleNode = OpenSearchNode(env, listOf(Netty4Plugin::class.java))
            openSearchSingleNode.start()
        }

        @OptIn(ExperimentalPathApi::class)
        @JvmStatic
        @AfterAll
        fun tearDown() {
            openSearchSingleNode.close()
            Paths.get(BASE_DATA_PATH, NODE_NAME).deleteRecursively()
        }

        private fun getOsSettings(): Settings? {
            val homePath = Paths.get(BASE_DATA_PATH, NODE_NAME)
            val settingsBuilder = Settings.builder()
            settingsBuilder.put("path.home", homePath.toString())
            settingsBuilder.putList("node.roles", "cluster_manager", "data")
            settingsBuilder.put("http.port", "9200")
            settingsBuilder.put("index.store.type", "fs")
            return settingsBuilder.build()
        }
    }

    fun refreshOpenSearchIndices() {
        openSearchSingleNode
            .client()
            .admin()
            .indices()
            .refresh(RefreshRequest(openSearchProperties.journeyIndexName))
            .get()
    }

    @BeforeEach
    fun setup() {
        timeService.reset()
        meterRegistry.clear()
    }

    protected fun adjustTime(
        journey: PDJDatedJourney,
        offset: Duration? = null,
    ) = timeService.adjustJourneyTime(journey, offset)

    protected fun adjustTime(
        journey: DTODatedJourney,
        offset: Duration? = null,
    ) = timeService.adjustJourneyTime(journey, offset)

    fun printMetrics(): String {
        val msg =
            mutableListOf<String>()
                .apply {
                    add("<--- all metrics ---")
                    addAll(metricsMap.prettyList())
                    add("--- all metrics --->")
                }.joinToString(separator = "\n", postfix = "\n\n", prefix = "\n\n")
        log.info("Metrics: $msg")
        return msg
    }

    protected fun storeDatedJourneys(vararg datedJourneys: PDJDatedJourney): Int {
        var res = 0
        datedJourneys.forEach { res += storeDatedJourney(it.ref, it) }
        return res
    }

    protected fun storeDatedJourneys(journeys: Map<String, List<PDJDatedJourney>>): Int {
        var res = 0
        journeys.values.forEach { taskJourneys ->
            taskJourneys.forEach {
                res += storeDatedJourney(it.ref, it)
            }
        }
        return res
    }

    protected fun storeDatedJourney(
        key: String,
        datedJourney: PDJDatedJourney,
    ): Int {
        adjustTime(datedJourney)
        val contex = datedJourneyInputService.process(key, datedJourney)
        insightService.insight(contex)
        return contex.stored
    }

    protected fun assertJourneyExists(ref: String?): DTODatedJourney = assertJourneyRecordExists(ref).journey

    protected fun assertJourneyInputExists(ref: String?): DTODatedJourney = assertJourneyInputRecordExists(ref).journey

    protected fun assertJourneyRecordExists(ref: String?): InternalDatedJourney = assertNotNull(journeyRepo.fetchByRef(ref))

    protected fun assertJourneyInputRecordExists(ref: String?): DatedJourneyInputData = assertNotNull(journeyInputRepo.fetchByRef(ref))
}
