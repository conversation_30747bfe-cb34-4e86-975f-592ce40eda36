package no.ruter.tranop.app.test

import no.ruter.rdp.metrics.RdpMetricsService
import no.ruter.tranop.ScanMarker
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TestTimeService
import no.ruter.tranop.app.variance.deviation.api.reason.TrafficPortalService
import no.ruter.tranop.app.variance.deviation.api.reason.TrafficPortalServiceMock
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.Primary

@ComponentScan(
    basePackageClasses = [
        ScanMarker::class,
    ],
)
@TestConfiguration
class TestConfig {
    @Bean
    @Primary
    fun timeService(): TestTimeService = TestTimeService()

    @Bean
    @Primary
    fun insightService(rdpMetricsService: RdpMetricsService): InsightService =
        InsightService(
            rdpMetricsService,
        )

    @Bean
    @Primary
    fun trafficPortalService(): TrafficPortalService = TrafficPortalServiceMock()
}
