package no.ruter.tranop.app.common.dataflow.kafka

import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.tranop.app.event.journey.lifecycle.JourneyEventLifeCycle
import no.ruter.tranop.app.plan.journey.JourneyData
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyDeleteRoutine
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyLifeCycle
import no.ruter.tranop.app.plan.journey.output.DatedJourneyKafkaPublishRoutine
import no.ruter.tranop.app.plan.journey.output.DatedJourneyKafkaRepublishRoutine
import no.ruter.tranop.app.plan.journey.output.DatedJourneyKafkaTombstoneRoutine
import no.ruter.tranop.app.plan.link.output.entity.StopPointLinkEntityPublishRoutine
import no.ruter.tranop.app.plan.link.output.internal.StopPointLinkDTOPublishRoutine
import no.ruter.tranop.app.plan.stop.output.entity.StopPointEntityPublishRoutine
import no.ruter.tranop.app.plan.stop.output.internal.StopPointDTOPublishRoutine
import no.ruter.tranop.app.test.AbstractBaseTest
import org.junit.jupiter.api.BeforeEach
import org.springframework.beans.factory.annotation.Autowired

abstract class AbstractKafkaTest : AbstractBaseTest() {
    @Autowired
    lateinit var kafka: KafkaTestEnvironment

    @Autowired
    lateinit var kafkaConfigService: KafkaConfigService

    @Autowired
    lateinit var journeyEventLifeCycle: JourneyEventLifeCycle

    @Autowired
    @Deprecated("Need to stop using this. Need to use the specific lifecycle where needed")
    lateinit var datedJourneyLifeCycle: DatedJourneyLifeCycle

    @Autowired
    lateinit var deleteDatedJourneyRoutine: DatedJourneyDeleteRoutine

    @Autowired
    lateinit var publishDatedJourneyRoutine: DatedJourneyKafkaPublishRoutine

    @Autowired
    lateinit var publishStopPointRoutine: StopPointEntityPublishRoutine

    @Autowired
    lateinit var publishStopPointLinkRoutine: StopPointLinkEntityPublishRoutine

    @Autowired
    lateinit var publishStopPointLinkDTORoutine: StopPointLinkDTOPublishRoutine

    @Autowired
    lateinit var publishStopPointDTORoutine: StopPointDTOPublishRoutine

    @Autowired
    lateinit var tombstoneDatedJourneyRoutine: DatedJourneyKafkaTombstoneRoutine

    @Autowired
    lateinit var republishDatedJourneyRoutine: DatedJourneyKafkaRepublishRoutine

    final val vehicleTasks = JourneyData.VEHICLE_TASKS
    final val vehicleTask1 = vehicleTasks["8119-2023-01-12"]!!
    final val vehicleTask1datedJourney1 = vehicleTask1[0]
    final val vehicleTask1datedJourney2 = vehicleTask1[1]

    @BeforeEach
    override fun setup() {
        super.setup()
        kafka.cleanOutputTopics()
    }

    fun pipe(datedJourney: PDJDatedJourney) {
        pipe(datedJourney.ref, datedJourney)
    }

    fun pipe(
        key: String,
        datedJourney: PDJDatedJourney?,
    ) {
        val contex = datedJourneyInputService.process(key, datedJourney)
        insightService.insight(contex)
    }
}
