package no.ruter.tranop.app.deviation.entity

import no.ruter.avro.entity.operational.OperationalServiceDeviationKeyV1
import no.ruter.avro.entity.operational.deviation.ServiceDeviationV1
import no.ruter.avro.operational.common.MetadataEntry
import no.ruter.avro.operational.common.ServiceImpact
import no.ruter.avro.operational.deviation.ServiceDeviationReason
import no.ruter.avro.operational.deviation.ServiceDeviationSpecV1
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.dataflow.kafka.entity.AbstractEntityV2OutputMapper
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MappingDetails
import no.ruter.tranop.app.common.mapping.input.InputMessage
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.deviation.config.ServiceDeviationConfigProperties
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation
import org.springframework.stereotype.Component
import no.ruter.avro.operational.common.DateTimeRange as AvroDateTimeRange
import no.ruter.avro.operational.common.spec.JourneyLineSpecWindow as AvroJourneyLineSpecWindow
import no.ruter.avro.operational.common.spec.JourneySpecWindowOption as AvroJourneySpecWindowOption
import no.ruter.avro.operational.common.spec.StopPointSpecWindow as AvroStopPointSpecWindow

@Component
class ServiceDeviationEntityOutputMapper(
    config: ServiceDeviationConfigProperties,
    timeService: TimeService,
    insightService: InsightService,
) : AbstractEntityV2OutputMapper<
        DTOServiceDeviation,
        ServiceDeviationInputContext,
        OperationalServiceDeviationKeyV1,
        ServiceDeviationEntityOutputContext,
    >(
        RecordType.SERVICE_DEVIATION,
        config,
        timeService,
        insightService,
    ) {
    private val toggles = config.toggles

    override fun getOwnerId(value: OperationalServiceDeviationKeyV1?): String? = value?.entityHeader?.ownerId

    override fun createContext(input: InputMessage<DTOServiceDeviation, ServiceDeviationInputContext>) =
        ServiceDeviationEntityOutputContext(input)

    override fun mapValue(
        input: InputMessage<DTOServiceDeviation, ServiceDeviationInputContext>,
        context: ServiceDeviationEntityOutputContext,
    ): OperationalServiceDeviationKeyV1? {
        val path = PATH_ROOT
        val value = input.value!! // Tombstones never get here, so value is always non-null.

        val serviceDeviation = mapServiceDeviation(value, path, context)
        return context.build(path) {
            val header = createEntityHeader(context)

            OperationalServiceDeviationKeyV1
                .newBuilder()
                .setEntityData(serviceDeviation)
                .setEntityHeader(header)
                .build()
        }
    }

    private fun mapServiceDeviation(
        input: DTOServiceDeviation,
        path: String,
        context: ServiceDeviationEntityOutputContext,
    ): ServiceDeviationV1? {
        val ref = input.ref

        val impact =
            ServiceImpact
                .newBuilder()
                .setStopPoints(emptyList<AvroStopPointSpecWindow>())
                .setLines(emptyList<AvroJourneyLineSpecWindow>())
                .setJourneys(emptyList<AvroJourneySpecWindowOption>())
                .build()

        val reason =
            ServiceDeviationReason
                .newBuilder()
                .setCode(input.spec.reason?.code)
                .setComment(input.spec.reason?.comment)
                .build()

        val duration =
            input.spec.duration?.let { dtRange ->
                AvroDateTimeRange
                    .newBuilder()
                    .setStart(
                        dtRange.start
                            ?.toOffsetDateTime(
                                context,
                                "$path.spec.duration.start",
                                "duration.start",
                                MappingDetails.Type.ERROR,
                            )?.toString(),
                    ).setEnd(
                        dtRange.end
                            ?.toOffsetDateTime(
                                context,
                                "$path.spec.duration.end",
                                "duration.end",
                                MappingDetails.Type.ERROR,
                            )?.toString(),
                    ).build()
            }

        val spec =
            ServiceDeviationSpecV1
                .newBuilder()
                .setCode(input.spec.code?.value)
                .setImpact(impact)
                .setReason(reason)
                .setDuration(duration)
                .setMetadata(
                    input.spec.metadata?.map {
                        MetadataEntry(it.key?.value, it.value)
                    } ?: emptyList(),
                ).build()

        return context.build(path) {
            val builder =
                ServiceDeviationV1
                    .newBuilder()
                    .setRef(ref)
                    .setSpec(spec)

            builder.build()
        }
    }
}
