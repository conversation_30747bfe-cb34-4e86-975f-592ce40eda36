package no.ruter.tranop.app.deviation.entity

import no.ruter.avro.entity.operational.OperationalServiceDeviationKeyV1
import no.ruter.avro.entity.operational.deviation.ServiceDeviationV1
import no.ruter.avro.operational.common.MetadataEntry
import no.ruter.avro.operational.common.ServiceImpact

import no.ruter.avro.operational.deviation.ServiceDeviationReason
import no.ruter.avro.operational.deviation.ServiceDeviationSpecV1
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.dataflow.kafka.entity.AbstractEntityV2OutputMapper
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MappingDetails
import no.ruter.tranop.app.common.mapping.input.InputMessage
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.deviation.config.ServiceDeviationConfigProperties
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation
import org.springframework.stereotype.Component
import no.ruter.avro.operational.common.DateTimeRange as AvroDateTimeRange
import no.ruter.avro.operational.common.spec.JourneyLineSpecWindow as AvroJourneyLineSpecWindow
import no.ruter.avro.operational.common.spec.JourneySpecWindowOption as AvroJourneySpecWindowOption
import no.ruter.avro.operational.common.spec.StopPointSpecWindow as AvroStopPointSpecWindow
import no.ruter.avro.operational.common.spec.StopPointSpec as AvroStopPointSpec
import no.ruter.avro.operational.common.spec.JourneyLineSpec as AvroJourneyLineSpec
import no.ruter.avro.operational.common.spec.JourneySpec as AvroJourneySpec
import no.ruter.avro.operational.common.spec.JourneySpecWindow as AvroJourneySpecWindow
import no.ruter.avro.operational.common.spec.JourneyCallSpec as AvroJourneyCallSpec
import no.ruter.tranop.assignment.util.toOffsetDateTime

@Component
class ServiceDeviationEntityOutputMapper(
    config: ServiceDeviationConfigProperties,
    timeService: TimeService,
    insightService: InsightService,
) : AbstractEntityV2OutputMapper<
        DTOServiceDeviation,
        ServiceDeviationInputContext,
        OperationalServiceDeviationKeyV1,
        ServiceDeviationEntityOutputContext,
    >(
        RecordType.SERVICE_DEVIATION,
        config,
        timeService,
        insightService,
    ) {
    private val toggles = config.toggles

    override fun getOwnerId(value: OperationalServiceDeviationKeyV1?): String? = value?.entityHeader?.ownerId

    override fun createContext(input: InputMessage<DTOServiceDeviation, ServiceDeviationInputContext>) =
        ServiceDeviationEntityOutputContext(input)

    override fun mapValue(
        input: InputMessage<DTOServiceDeviation, ServiceDeviationInputContext>,
        context: ServiceDeviationEntityOutputContext,
    ): OperationalServiceDeviationKeyV1? {
        val path = PATH_ROOT
        val value = input.value!! // Tombstones never get here, so value is always non-null.

        val serviceDeviation = mapServiceDeviation(value, path, context)
        return context.build(path) {
            val header = createEntityHeader(context)

            OperationalServiceDeviationKeyV1
                .newBuilder()
                .setEntityData(serviceDeviation)
                .setEntityHeader(header)
                .build()
        }
    }

    private fun mapServiceDeviation(
        input: DTOServiceDeviation,
        path: String,
        context: ServiceDeviationEntityOutputContext,
    ): ServiceDeviationV1? {
        val ref = input.ref

        val impact =
            ServiceImpact
                .newBuilder()
                .setStopPoints(
                    input.spec.impact?.stopPoints?.mapNotNull {
                        mapStopPointSpecWindow(it, "$path.spec.impact.stopPoints", context)
                    } ?: emptyList()
                )
                .setLines(
                    input.spec.impact?.lines?.mapNotNull {
                        mapJourneyLineSpecWindow(it, "$path.spec.impact.lines", context)
                    } ?: emptyList()
                )
                .setJourneys(
                    input.spec.impact?.journeys?.mapNotNull {
                        mapJourneySpecWindowOption(it, "$path.spec.impact.journeys", context)
                    } ?: emptyList()
                )
                .build()

        val reason =
            ServiceDeviationReason
                .newBuilder()
                .setCode(input.spec.reason?.code)
                .setComment(input.spec.reason?.comment)
                .build()

        val duration =
            input.spec.duration?.let { dtRange ->
                AvroDateTimeRange
                    .newBuilder()
                    .setStart(
                        dtRange.start
                            ?.toOffsetDateTime(
                                context,
                                "$path.spec.duration.start",
                                "duration.start",
                                MappingDetails.Type.ERROR,
                            )?.toString(),
                    ).setEnd(
                        dtRange.end
                            ?.toOffsetDateTime(
                                context,
                                "$path.spec.duration.end",
                                "duration.end",
                                MappingDetails.Type.ERROR,
                            )?.toString(),
                    ).build()
            }

        val spec =
            ServiceDeviationSpec
                .newBuilder()
                .setCode(input.spec.code?.value)
                .setImpact(impact)
                .setReason(reason)
                .setDuration(duration)
                .setMetadata(
                    input.spec.metadata?.map {
                        MetadataEntry(it.key?.value, it.value)
                    } ?: emptyList(),
                ).build()

        return context.build(path) {
            val builder =
                ServiceDeviationV1
                    .newBuilder()
                    .setRef(ref)
                    .setSpec(spec)

            builder.build()
        }
    }

    private fun mapStopPointSpecWindow(
        input: no.ruter.tranop.operation.common.dto.model.spec.DTOStopPointSpecWindow,
        path: String,
        context: ServiceDeviationEntityOutputContext,
    ): AvroStopPointSpecWindow? {
        val spec = input.spec?.let { mapStopPointSpec(it, "$path.spec", context) }
        val serviceWindow = input.serviceWindow?.let { mapDateTimeRange(it, "$path.serviceWindow", context) }

        return AvroStopPointSpecWindow.newBuilder()
            .setSpec(spec)
            .setServiceWindow(serviceWindow)
            .build()
    }

    private fun mapStopPointSpec(
        input: no.ruter.tranop.operation.common.dto.model.spec.DTOStopPointSpec,
        path: String,
        context: ServiceDeviationEntityOutputContext,
    ): AvroStopPointSpec? {
        return AvroStopPointSpec.newBuilder()
            .setEntityDatedJourneyStopPointKeyV2Ref(input.stopPointId)
            .setNsrQuayRef(input.quayId)
            .build()
    }

    private fun mapJourneyLineSpecWindow(
        input: no.ruter.tranop.operation.common.dto.model.spec.DTOJourneyLineSpecWindow,
        path: String,
        context: ServiceDeviationEntityOutputContext,
    ): AvroJourneyLineSpecWindow? {
        val spec = input.spec?.let { mapJourneyLineSpec(it, "$path.spec", context) }
        val serviceWindow = input.serviceWindow?.let { mapDateTimeRange(it, "$path.serviceWindow", context) }

        return AvroJourneyLineSpecWindow.newBuilder()
            .setSpec(spec)
            .setServiceWindow(serviceWindow)
            .build()
    }

    private fun mapJourneyLineSpec(
        input: no.ruter.tranop.operation.common.dto.model.spec.DTOJourneyLineSpec,
        path: String,
        context: ServiceDeviationEntityOutputContext,
    ): AvroJourneyLineSpec? {
        return AvroJourneyLineSpec.newBuilder()
            .setLineId(input.lineId)
            .setDirection(input.direction?.value)
            .build()
    }

    private fun mapJourneySpecWindowOption(
        input: no.ruter.tranop.operation.common.dto.model.spec.DTOJourneySpecWindowOption,
        path: String,
        context: ServiceDeviationEntityOutputContext,
    ): AvroJourneySpecWindowOption? {
        return AvroJourneySpecWindowOption.newBuilder()
            .setCalls(input.calls?.mapNotNull { mapJourneyCallSpec(it, "$path.calls", context) } ?: emptyList())
            .setJourney(input.journey?.let { mapJourneySpecWindow(it, "$path.journey", context) })
            .build()
    }

    private fun mapJourneySpecWindow(
        input: no.ruter.tranop.operation.common.dto.model.spec.DTOJourneySpecWindow,
        path: String,
        context: ServiceDeviationEntityOutputContext,
    ): AvroJourneySpecWindow? {
        return AvroJourneySpecWindow.newBuilder()
            .setSpec(input.spec?.let { mapJourneySpec(it, "$path.spec", context) })
            .setServiceWindow(input.serviceWindow?.let { mapDateTimeRange(it, "$path.serviceWindow", context) })
            .build()
    }

    private fun mapJourneySpec(
        input: no.ruter.tranop.operation.common.dto.model.spec.DTOJourneySpec,
        path: String,
        context: ServiceDeviationEntityOutputContext,
    ): AvroJourneySpec? {
        return AvroJourneySpec.newBuilder()
            .setLineId(input.lineId)
            .setJourneyId(input.journeyId)
            .build()
    }

    private fun mapJourneyCallSpec(
        input: no.ruter.tranop.operation.common.dto.model.spec.DTOJourneyCallSpec,
        path: String,
        context: ServiceDeviationEntityOutputContext,
    ): AvroJourneyCallSpec? {
        return AvroJourneyCallSpec.newBuilder()
            .setStopPoint(input.stopPoint?.let { mapStopPointSpec(it, "$path.stopPoint", context) })
            .setArrivalDateTime(input.arrivalDateTime)
            .setDepartureDateTime(input.departureDateTime)
            .build()
    }

    private fun mapDateTimeRange(
        input: no.ruter.tranop.common.dto.model.DTODateTimeRange,
        path: String,
        context: ServiceDeviationEntityOutputContext,
    ): AvroDateTimeRange? {
        return AvroDateTimeRange.newBuilder()
            .setStart(input.start?.toOffsetDateTime(context, "$path.start", "dateTimeRange.start", MappingDetails.Type.ERROR)?.toString())
            .setEnd(input.end?.toOffsetDateTime(context, "$path.end", "dateTimeRange.end", MappingDetails.Type.ERROR)?.toString())
            .build()
    }
}
