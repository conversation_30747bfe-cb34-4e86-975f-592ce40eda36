package no.ruter.tranop.app.deviation.entity

import no.ruter.avro.entity.operational.OperationalServiceDeviationKey
import no.ruter.avro.entity.operational.deviation.ServiceDeviation
import no.ruter.avro.operational.common.MetadataEntry
import no.ruter.avro.operational.deviation.ServiceDeviationImpact
import no.ruter.avro.operational.deviation.ServiceDeviationSpec
import no.ruter.avro.operational.deviation.common.ServiceDeviationReason
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.dataflow.kafka.entity.AbstractEntityV2OutputMapper
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.input.InputMessage
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.deviation.config.ServiceDeviationConfigProperties
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation
import org.springframework.stereotype.Component

@Component
class ServiceDeviationEntityOutputMapper(
    config: ServiceDeviationConfigProperties,
    timeService: TimeService,
    insightService: InsightService,
) : AbstractEntityV2OutputMapper<
        DTOServiceDeviation,
        ServiceDeviationInputContext,
        OperationalServiceDeviationKey,
        ServiceDeviationEntityOutputContext,
    >(
        RecordType.SERVICE_DEVIATION,
        config,
        timeService,
        insightService,
    ) {
    private val toggles = config.toggles

    override fun getOwnerId(value: OperationalServiceDeviationKey?): String? = value?.entityHeader?.ownerId

    override fun createContext(input: InputMessage<DTOServiceDeviation, ServiceDeviationInputContext>) =
        ServiceDeviationEntityOutputContext(input)

    override fun mapValue(
        input: InputMessage<DTOServiceDeviation, ServiceDeviationInputContext>,
        context: ServiceDeviationEntityOutputContext,
    ): OperationalServiceDeviationKey? {
        val path = PATH_ROOT
        val value = input.value!! // Tombstones never get here, so value is always non-null.

        val serviceDeviation = mapServiceDeviation(value, path, context)
        return context.build(path) {
            val header = createEntityHeader(context)

            OperationalServiceDeviationKey
                .newBuilder()
                .setEntityData(serviceDeviation)
                .setEntityHeader(header)
                .build()
        }
    }

    protected fun mapServiceDeviation(
        input: DTOServiceDeviation,
        path: String,
        context: ServiceDeviationEntityOutputContext,
    ): ServiceDeviation? {
        val ref = input.ref

        val impact =
            ServiceDeviationImpact
                .newBuilder()
                .setStopPoints(
                    input.spec.impact.stopPoints
                        .map { it.spec.stopPointId },
                ).setLines(
                    input.spec.impact.lines
                        .map { it.spec.lineId },
                ).setJourneys(
                    input.spec.impact.journeys
                        .map { it.journey.spec.journeyId },
                ).build()

        val reason =
            ServiceDeviationReason
                .newBuilder()
                .setCode(input.spec.reason.code)
                .setComment(input.spec.reason.comment)
                .build()

        val spec =
            ServiceDeviationSpec
                .newBuilder()
                .setCode(input.spec.code.value)
                .setImpact(impact)
                .setReason(reason)
                .setDuration("${input.spec.duration.start}-${input.spec.duration.end}")
                .setMetadata(
                    input.spec.metadata.map {
                        MetadataEntry(it.key.value, it.value)
                    },
                ).build()

        return context.build(path) {
            val builder =
                ServiceDeviation
                    .newBuilder()
                    .setRef(ref)
                    .setSpec(spec)

            builder.build()
        }
    }
}
