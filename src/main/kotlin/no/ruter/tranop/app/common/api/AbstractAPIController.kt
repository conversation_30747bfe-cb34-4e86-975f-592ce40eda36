package no.ruter.tranop.app.common.api

import no.ruter.rdp.logging.LoggerFactory
import no.ruter.rdp.metrics.InsightUtils
import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.TraceInfoContext
import no.ruter.tranop.app.common.api.exception.APIException
import no.ruter.tranop.app.common.api.exception.APIResourceNotFoundException
import no.ruter.tranop.app.common.insight.InsightContext
import no.ruter.tranop.app.common.insight.InsightService
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity

abstract class AbstractAPIController(
    protected val channel: DataChannel,
    protected val insightService: InsightService,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    protected fun <T, C : TraceInfoContext> execute(
        name: String,
        context: C,
        action: APIAction<C, T>,
        badRequestHandler: APIBadRequestHandler<T>,
        internalErrorHandler: APIInternalErrorHandler<T>,
    ): ResponseEntity<T> =
        execute(
            name = name,
            context = context,
            action = action,
            notFoundHandler = { s, m, e -> badRequestHandler(s, m, IllegalArgumentException(e.message, e)) },
            badRequestHandler = badRequestHandler,
            internalErrorHandler = internalErrorHandler,
        )

    protected fun <T, C : TraceInfoContext> execute(
        name: String,
        context: C,
        action: APIAction<C, T>,
        notFoundHandler: APINotFoundHandler<T>,
        badRequestHandler: APIBadRequestHandler<T>,
        internalErrorHandler: APIInternalErrorHandler<T>,
    ): ResponseEntity<T> {
        fun recordInsight(
            s: HttpStatus,
            res: ResponseEntity<T>,
            cause: Exception? = null,
        ) = recordInsight(name, s, context, res, cause)

        return try {
            context.resolveActiveOperator() // Ensure authority and operator are fully resolved before calling our action.
            val (body, s, h) = action(context)
            val res = ResponseEntity.status(s).headers(h).body(body)
            recordInsight(s, res)
        } catch (e: APIResourceNotFoundException) {
            val s = HttpStatus.NOT_FOUND
            val res = notFoundHandler(s, e.message, e)
            recordInsight(s, res, cause = e)
        } catch (e: APIException) {
            val s = e.httpStatus
            if (s.is4xxClientError) {
                val res =
                    if (s == HttpStatus.NOT_FOUND) {
                        notFoundHandler(s, e.message, APIResourceNotFoundException(e.message ?: "<no message>", e))
                    } else {
                        badRequestHandler(s, e.message, IllegalArgumentException(e.message, e))
                    }
                recordInsight(s, res, cause = e)
            } else {
                val res = internalErrorHandler(s, e.message, e)
                recordInsight(s, res, cause = e)
            }
        } catch (e: IllegalArgumentException) {
            val s = HttpStatus.BAD_REQUEST
            val res = badRequestHandler(s, e.message, e)
            recordInsight(s, res, cause = e)
        } catch (e: UnsupportedOperationException) {
            val s = HttpStatus.NOT_IMPLEMENTED
            val res = internalErrorHandler(s, e.message, e)
            recordInsight(s, res, cause = e)
        } catch (e: Exception) {
            val s = HttpStatus.INTERNAL_SERVER_ERROR
            val res = internalErrorHandler(s, e.message, e)
            recordInsight(s, res, cause = e)
        }
    }

    private fun <T, C : InsightContext> recordInsight(
        name: String,
        s: HttpStatus,
        context: C,
        res: ResponseEntity<T>,
        cause: Exception? = null,
    ): ResponseEntity<T> {
        val insight = InsightUtils.normalize(value = "${channel.insightKey}.$name.${s.insightValue()}")
        if (s.isError) {
            val msg = "${s.value()} ${s.reasonPhrase}: ${cause?.message ?: insight}"
            when (s.series()) {
                HttpStatus.Series.CLIENT_ERROR -> {
                    logger.info(msg = msg, metadata = context.metadata)
                }
                HttpStatus.Series.SERVER_ERROR -> {
                    context.exception = cause
                    logger.error(msg = msg, metadata = context.metadata, e = cause)
                }
                else -> { }
            }
        }

        insightService.insight(context)
        return res
    }
}
