package no.ruter.tranop.app.common.db.record.base

import no.ruter.rdp.logging.Logger
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.db.xref.ExternalRefRepository
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.assignment.common.db.record.base.BaseRecord
import org.jooq.Condition
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.Record1
import org.jooq.RecordMapper
import org.jooq.Select
import org.jooq.SelectForUpdateStep
import org.jooq.SortField
import org.jooq.Table
import org.jooq.UpdatableRecord
import java.time.OffsetDateTime

abstract class BaseRecordRepository<R, T : Table<R>, E, M : RecordMapper<R, E>>(
    val recordType: RecordType<T, R>,
    protected val dslContext: DSLContext,
    private val sortFields: List<SortField<out Any>>,
    protected val recordMapper: M,
    protected val timeService: TimeService,
    protected val insightService: InsightService,
) where R : BaseRecord, R : UpdatableRecord<R> {
    protected val log: Logger = LoggerFactory.Companion.getLogger(javaClass.canonicalName)

    val table = recordType.table
    val tableMeta = recordType.tableMeta
    val externalRefRepo = ExternalRefRepository(recordType, dslContext, insightService)

    fun fetch(query: AbstractQueryBuilder<R, T, *>): List<E> {
        val c = query.build()
        return query(c.condition, c.page).fetch(recordMapper)
    }

    fun fetch(sql: Select<out Record>): List<E> =
        sql.fetch { record ->
            val typedRecord = record.into(table)
            recordMapper.map(typedRecord)
        }

    fun fetch(
        cond: Condition? = null,
        pagination: AbstractQueryBuilder.Pagination? = null,
    ): List<E> = query(cond, pagination).fetch(recordMapper)

    fun fetchByRef(ref: String?): E? = ref?.let { fetch(cond = tableMeta.ref.eq(it)).firstOrNull() }

    fun findRefs(cond: Condition?): Set<String> =
        cond?.let { c ->
            dslContext
                .select(tableMeta.ref)
                .from(table)
                .where(cond)
                .fetchSet(Record1<String>::value1)
        } ?: emptySet()

    private fun query(
        cond: Condition? = null,
        pagination: AbstractQueryBuilder.Pagination? = null,
    ): SelectForUpdateStep<R> {
        val select = if (cond == null) dslContext.selectFrom(table) else dslContext.selectFrom(table).where(cond)
        return select
            .orderBy(sortFields)
            .limit(pagination?.limit)
            .offset(pagination?.offset)
    }

    fun exists(condition: Condition?): Boolean {
        condition ?: return false
        return dslContext.fetchExists(table, condition)
    }

    fun count(cond: Condition? = null): Int =
        dslContext
            .fetchCount(table, cond)

    fun fetchAll(): List<E> = fetch()

    protected fun delete(condition: Condition): Int =
        dslContext
            .delete(table)
            .where(condition)
            .execute()

    fun deleteByRefs(
        refs: Collection<String>,
        deleteExternalRefs: Boolean = false,
    ): Int {
        val deleted = delete(tableMeta.ref.`in`(refs))
        if (deleteExternalRefs) {
            externalRefRepo.deleteByOwnerRefs(refs)
        }
        return deleted
    }

    fun setDeleted(
        ref: String?,
        trace: TraceInfo, // TODO: Record actual trace for who deleted the record...
        now: OffsetDateTime? = null,
    ): Boolean {
        val deleted = tableMeta.deleted ?: throw missingFieldException(desc = "deleted")
        val revision = tableMeta.revision ?: throw missingFieldException(desc = "revision")
        val deletedRevision = tableMeta.deletedRevision ?: throw missingFieldException(desc = "deleted revision")

        val t = now ?: timeService.now()
        val newRevision = revision.plus(1)

        val stmt =
            dslContext
                .update(table)
                .set(deleted, t)
                .set(revision, newRevision)
                .set(deletedRevision, newRevision)
                .where(tableMeta.ref.eq(ref))

        return stmt.execute() == 1
    }

    protected fun missingFieldException(desc: String) =
        IllegalStateException("field(s) [$desc] not supported by table: ${tableMeta.table.name}")
}
