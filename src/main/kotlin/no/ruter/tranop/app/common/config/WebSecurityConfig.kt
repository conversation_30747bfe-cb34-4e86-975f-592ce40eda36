package no.ruter.tranop.app.common.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpMethod
import org.springframework.security.config.Customizer
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.web.SecurityFilterChain
import org.springframework.web.cors.CorsConfiguration
import org.springframework.web.cors.CorsConfigurationSource
import org.springframework.web.cors.UrlBasedCorsConfigurationSource

@Configuration
@EnableWebSecurity
class WebSecurityConfig {
    companion object {
        private const val API_PATTERN = "/api/**"

        private val ACTUATOR_PATTERNS =
            arrayOf(
                "/actuator/info",
                "/actuator/health",
            )
        private const val ACTUATOR_LOGGER_PATTERN = "/actuator/loggers/**"
    }

    @Bean
    fun corsConfigurationSource(): CorsConfigurationSource {
        val configuration = CorsConfiguration()
        configuration.allowedOrigins = listOf("*")
        configuration.allowedMethods = setOf(HttpMethod.GET, HttpMethod.POST).map(HttpMethod::name)
        val source = UrlBasedCorsConfigurationSource()
        source.registerCorsConfiguration(API_PATTERN, configuration)
        return source
    }

    @Bean
    @Throws(Exception::class)
    fun securityFilterChain(http: HttpSecurity): SecurityFilterChain =
        http
            .csrf { csrf ->
                csrf.ignoringRequestMatchers(API_PATTERN, ACTUATOR_LOGGER_PATTERN)
            }.authorizeHttpRequests { auth ->
                auth
                    .requestMatchers(HttpMethod.GET, API_PATTERN)
                    .permitAll()
                    .requestMatchers(HttpMethod.PUT, API_PATTERN)
                    .permitAll()
                    .requestMatchers(HttpMethod.POST, API_PATTERN)
                    .permitAll()
                    .requestMatchers(HttpMethod.DELETE, API_PATTERN)
                    .permitAll()
                    .requestMatchers(HttpMethod.OPTIONS, API_PATTERN)
                    .permitAll()
                    .requestMatchers(HttpMethod.GET, *ACTUATOR_PATTERNS)
                    .permitAll()
                    .anyRequest()
                    .authenticated()
            }.sessionManagement { session ->
                session.sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            }.httpBasic(Customizer.withDefaults())
            .build()
}
