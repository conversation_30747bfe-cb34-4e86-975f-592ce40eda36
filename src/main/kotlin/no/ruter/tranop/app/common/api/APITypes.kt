package no.ruter.tranop.app.common.api

import no.ruter.rdp.metrics.InsightUtils
import no.ruter.tranop.app.common.api.exception.APIResourceNotFoundException
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity

typealias APIAction<C, T> = (C) -> APIActionResponse<T>
typealias APIActionResponse<T> = Triple<T, HttpStatus, HttpHeaders>
typealias APINotFoundHandler<T> = (HttpStatus, String?, APIResourceNotFoundException) -> ResponseEntity<T>
typealias APIBadRequestHandler<T> = (HttpStatus, String?, IllegalArgumentException) -> ResponseEntity<T>
typealias APIInternalErrorHandler<T> = (HttpStatus, String?, Exception) -> ResponseEntity<T>

fun HttpStatus.insightValue(): String = "${value()}.${InsightUtils.normalize(reasonPhrase)}"
