package no.ruter.tranop.app.event.journey.output

import jakarta.annotation.PreDestroy
import net.snowflake.ingest.streaming.SnowflakeStreamingIngestChannel
import net.snowflake.ingest.streaming.SnowflakeStreamingIngestClient
import no.ruter.rdp.common.json.JsonUtils
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientFactory
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientProperties
import no.ruter.tranop.app.event.journey.input.JourneyEventInputContext
import no.ruter.tranop.dated.journey.bi.BIEventMetadataKeyType
import no.ruter.tranop.journey.event.bi.model.BIEventMetadata
import no.ruter.tranop.journey.event.bi.model.BIJourneyEvent
import org.springframework.stereotype.Service
import java.time.Instant.now

/**
 * Client for streaming ingestion of journey events into Snowflake.
 *
 * @property config Configuration for streaming ingest.
 * @property overrideClient Optional Snowflake client for dependency injection for testing.
 */
@Service
class JourneyEventSnowflakeIngestClient(
    private val config: SnowflakeClientProperties,
    private val clientFactory: SnowflakeClientFactory,
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    // TODO: Move this to abstract base class.
    private val client: SnowflakeStreamingIngestClient? =
        if (config.enabled) clientFactory.createClient(CLIENT_BUILDER_NAME, config) else null

    // TODO: Move this to abstract base class.
    private val channel: SnowflakeStreamingIngestChannel? = client?.let { clientFactory.createChannel(CHANNEL_BUILDER_NAME, it, config) }

    companion object {
        const val CLIENT_BUILDER_NAME = "ASSIGNMENT_DJV2_DEV_EVENTS"
        const val CHANNEL_BUILDER_NAME = "ASSIGNMENT_DJV2_DEV_JOURNEY_EVENT_CHANNEL"
    }

    // TODO: Make this generic and move it to abstract base class.
    fun ingest(events: List<Pair<String, BIJourneyEvent?>>): Pair<List<String>, List<String>> {
        if (channel == null || events.isEmpty()) {
            return Pair(emptyList(), emptyList())
        }

        val outboxRefs = events.map { it.first }
        val offsetToken = outboxRefs.last() // Note: we use ref from key in pair instead of fetching from value, since value may be null.
        val result =
            channel.insertRows(
                events.map { it.second?.toIngestMap() },
                offsetToken,
            )

        if (!verifyOffset(channel, offsetToken)) {
            logger.warn(
                "Could not verify offset when ingesting data to Snowflake. Marking all failed with insertErrors: ${result.insertErrors}",
            )
            return Pair(emptyList(), events.map { it.first })
        }

        if (result.hasErrors()) {
            logger.warn("Error ingesting data to Snowflake: ${result.insertErrors}")
            val errorRefs =
                result.insertErrors.map { error ->
                    events[error.rowIndex.toInt()].first
                }
            return Pair(outboxRefs - errorRefs, errorRefs)
        }

        logger.debug("Successfully ingested ${events.size} events to Snowflake")
        return Pair(
            outboxRefs,
            emptyList(),
        )
    }

    // TODO: Move this to abstract base class.
    @PreDestroy
    fun shutdown() {
        runCatching {
            channel?.close(true)
            client?.close()
        }.onFailure { e ->
            logger.error("Failed to close Snowflake ingest client on shutdown", e)
        }
    }

    // TODO: Move this to abstract base class.
    private fun verifyOffset(
        channel: SnowflakeStreamingIngestChannel,
        offsetToken: String,
    ): Boolean {
        repeat(20) {
            if (channel.latestCommittedOffsetToken == offsetToken) return true
            Thread.sleep(200)
        }
        return false
    }
}

fun JourneyEventInputContext.toBIJourneyEvent(): BIJourneyEvent =
    BIJourneyEvent(
        assignmentRef,
        now().toString(),
        datedJourneyV2Ref,
        metadata.toBIEventMetadataList(),
        ref,
        journeyEventType.name,
    )

private fun Map<String, Any?>.toBIEventMetadataList(): List<BIEventMetadata> =
    mapNotNull { (key, value) ->
        BIEventMetadata(BIEventMetadataKeyType.of(key), value).takeIf {
            BIEventMetadataKeyType.ALL.contains(it.key)
        }
    }

private fun BIJourneyEvent.toIngestMap(): Map<String, Any> =
    mapOf(
        "ASSIGNMENT_REF" to assignmentRef,
        "CREATED_AT" to createdAt,
        "DATED_JOURNEY_V2_REF" to datedJourneyV2Ref,
        "METADATA" to JsonUtils.toJson(metadata.associate { it.key to it.value }),
        "REF" to ref,
        "TYPE" to type,
    )
