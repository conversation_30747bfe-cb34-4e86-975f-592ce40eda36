package no.ruter.tranop.app.event.journey.input.process.mitigation

import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.common.time.TimeUtils
import no.ruter.tranop.app.event.journey.config.JourneyEventConfig
import no.ruter.tranop.app.event.journey.input.process.AbstractJourneyEventHandler
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCall
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.dated.journey.dto.model.common.DTOStopPointBehaviourType
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventCancellation

class JourneyCancellationEventHandler(
    config: JourneyEventConfig.CancellationJourneyEventConfig,
) : AbstractJourneyEventHandler(config) {
    companion object {
        private val eventMetadataFields =
            mapOf(
                DTOEventMetadataKeyType.QUAY_REFS
                    to DTOJourneyEventCancellation::getQuayRefs,
                DTOEventMetadataKeyType.ENTITY_TRAFFIC_CASE_KEY_V2_REF
                    to DTOJourneyEventCancellation::getEntityTrafficCaseKeyV2Ref,
                DTOEventMetadataKeyType.ENTITY_TRAFFIC_EVENT_KEY_V1_REF
                    to DTOJourneyEventCancellation::getEntityTrafficEventKeyV1Ref,
                DTOEventMetadataKeyType.ENTITY_TRAFFIC_SITUATION_KEY_V2_REF
                    to DTOJourneyEventCancellation::getEntityTrafficSituationKeyV2Ref,
            )
    }

    override fun handleJourneyEvent(
        context: ProcessingContext,
        event: DTOJourneyEvent,
        journey: DTODatedJourney,
    ): DTOEvent? {
        event.cancellation?.let { cancellation ->
            val partial = cancellation.partial ?: false
            val cancelled = cancellation.cancelled ?: false

            val calls = journey.plan.calls
            val stopPoints = journey.plan.stops.associateBy { it.ref }

            if (partial) {
                calls.forEach { call ->
                    call.cancelled = isCallCancelled(call, stopPoints, cancellation)
                }
            } else {
                calls.forEach { it.cancelled = cancelled }
            }
            val allCallsCancelled = calls.all { it.cancelled ?: false }
            val noCallsCancelled = calls.none { it.cancelled ?: false }
            val partiallyCancelled = calls.any { it.cancelled ?: false } && !allCallsCancelled

            journey.cancelled = allCallsCancelled
            journey.journeyState?.journeyMitigationState?.apply {
                this.cancelled = allCallsCancelled
                this.partiallyCancelled = partiallyCancelled
            }

            updateBehaviourType(calls)

            val type =
                when {
                    partial && noCallsCancelled -> DTOEventType.CALLS_UNCANCELLED
                    partial -> DTOEventType.CALLS_CANCELLED
                    allCallsCancelled -> DTOEventType.CANCELLED
                    noCallsCancelled -> DTOEventType.UNCANCELLED
                    else -> DTOEventType.CALLS_CANCELLED
                }

            val metadata = collect(eventMetadataFields, cancellation)

            return createEvent(
                context = context,
                event = event,
                type = type,
                metadata = metadata,
                msg = event.status?.description,
                source = "journey-event/operational",
            )
        }
        return null
    }

    private fun isCallCancelled(
        call: DTODatedJourneyCall,
        stopPoints: Map<String, DTOStopPoint>,
        cancellation: DTOJourneyEventCancellation,
    ): Boolean {
        if (cancellation.cancelled != true) return false
        val stopPoint = stopPoints[call.stopPointRef] ?: return false

        val arrival = TimeUtils.removeSeconds(call.plannedArrival?.toOffsetDateTime())
        val departure = TimeUtils.removeSeconds(call.plannedDeparture?.toOffsetDateTime())
        for (c in cancellation.calls) {
            if (c.nsrQuayId == stopPoint.quayRef) {
                TimeUtils.removeSeconds(c.time?.toOffsetDateTime())?.let { time ->
                    if (arrival?.isEqual(time) == true) return true
                    if (departure?.isEqual(time) == true) return true
                }
            }
        }
        return false
    }

    /**
     * Update behaviour type of `calls` based on cancellation status of individual calls
     * and consecutive cancelled calls at the start of the call list or at the end of call list.
     * The first call *after* the last consecutively cancelled call at the start of the list will be updated with
     * `FOR_BOARDING_ONLY`, unless it already has `NO_SERVICE`.
     *
     * The last call *before* the last consecutively cancelled call at the end of the list will be updated with
     * `FOR_ALIGHTING_ONLY`, unless it already has `NO_SERVICE`.
     * All cancelled calls will be updated with `NO_SERVICE`.
     **/
    private fun updateBehaviourType(calls: List<DTODatedJourneyCall>) {
        // Exactly same logic exists also in Assignment Manager and behavior is expected to be the same
        // so, in case any changes they should be reflected in Assignment manager
        var cancelledCounter = 0

        calls.forEach { call ->
            if (call.cancelled == null || call.cancelled == false) {
                call.originalStopPointBehaviourType?.let {
                    call.stopPointBehaviourType = call.originalStopPointBehaviourType
                }
            } else {
                call.stopPointBehaviourType = DTOStopPointBehaviourType.NO_SERVICE
                cancelledCounter++
            }
        }

        val journeyCancelled = calls.all { it.cancelled == true }
        if (journeyCancelled) {
            return
        }

        calls.firstOrNull()?.let { first ->
            when {
                // journey shortened at the start
                first.cancelled == true -> {
                    calls
                        .find {
                            it.stopPointBehaviourType != DTOStopPointBehaviourType.NO_SERVICE
                        }?.stopPointBehaviourType = DTOStopPointBehaviourType.FOR_BOARDING_ONLY
                }
                // otherwise first stop would always be FOR_BOARDING_ONLY according to natural order of things
                first.stopPointBehaviourType == DTOStopPointBehaviourType.FULL_SERVICE -> {
                    first.stopPointBehaviourType = DTOStopPointBehaviourType.FOR_BOARDING_ONLY
                }
            }
        }

        calls.lastOrNull()?.let { last ->
            when {
                // journey shortened at the end
                last.cancelled == true -> {
                    calls
                        .findLast {
                            it.stopPointBehaviourType != DTOStopPointBehaviourType.NO_SERVICE
                        }?.stopPointBehaviourType = DTOStopPointBehaviourType.FOR_ALIGHTING_ONLY
                }
                // otherwise last stop would always be FOR_ALIGHTING_ONLY according to natural order of things
                last.stopPointBehaviourType == DTOStopPointBehaviourType.FULL_SERVICE -> {
                    last.stopPointBehaviourType = DTOStopPointBehaviourType.FOR_ALIGHTING_ONLY
                }
            }
        }
    }
}
