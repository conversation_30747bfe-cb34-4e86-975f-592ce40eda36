package no.ruter.tranop.app.variance.mitigation.db

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.mapping.deepCopy
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.MitigationRecord
import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigation
import org.jooq.JSON
import org.jooq.RecordMapper
import java.time.OffsetDateTime

class ServiceMitigationRecordMapper(
    val insightService: InsightService,
) : RecordMapper<MitigationRecord, InternalServiceMitigation> {
    override fun map(record: MitigationRecord): InternalServiceMitigation {
        val data = JsonUtils.toObject(record.jsonData?.data(), DTOServiceMitigation::class.java)
        return InternalServiceMitigation(
            data = data,
            record = record,
        )
    }

    fun updateRecord(
        source: DTOServiceMitigation,
        record: MitigationRecord,
        trace: TraceInfo,
        now: OffsetDateTime,
    ) {
        record.ref = source.ref
        record.jsonHash = source.hash()
        record.jsonData = JSON.valueOf(JsonUtils.toJson(source))

        val creator = trace.operatorId
        if (record.createdAt == null) {
            record.createdAt = now
            record.createdBy = creator
            record.operatorId = trace.operatorId
            record.authorityId = trace.authorityId
        }
        record.deletedAt = null
        record.deletedRevision = null

        record.modifiedAt = now
        record.modifiedBy = creator
    }

    companion object {
        fun DTOServiceMitigation?.hash(): String =
            this?.let {
                val stripped =
                    it
                        .deepCopy()
                        .apply {
                            this?.header = null
                            this?.lifecycle = null
                        }
                MapperUtils.hash(JsonUtils.toJson(stripped))
            } ?: "null"
    }
}
