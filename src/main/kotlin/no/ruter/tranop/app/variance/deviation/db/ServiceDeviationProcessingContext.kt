package no.ruter.tranop.app.variance.deviation.db

import no.ruter.rdp.logging.LogKey
import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.AbstractDbProcessingContext
import no.ruter.tranop.app.common.mapping.toStatus
import no.ruter.tranop.assignment.dto.model.value.DTOStatusCode
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation

class ServiceDeviationProcessingContext(
    val link: DTOServiceDeviation,
    val ref: String? = link.ref,
    override val channel: DataChannel = RecordType.SERVICE_DEVIATION.channels.input,
    override val recordMetadata: Boolean = true,
) : AbstractDbProcessingContext() {
    override val traceId = link.header?.traceId

    override val metadata: Map<String, Any?>
        get() =
            mapOf(
                LogKey.TRACE_ID to traceId,
                "insert" to insert,
                "update" to update,
                "duplicate" to duplicate,
                "json_diff" to jsonDiff?.asString(),
            ).filterValues { it != null }

    override val summary: String
        get() =
            listOf(
                when {
                    insert -> "insert"
                    update -> "update"
                    duplicate -> "duplicate"
                    else -> "none"
                },
                traceId,
            ).joinToString(" / ")

    override val insightKey: String
        get() = "${channel.insightKey}.${statusDetails.toStatus().code?.value ?: DTOStatusCode.UNKNOWN}"
}
