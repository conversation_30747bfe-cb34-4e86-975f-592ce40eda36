package no.ruter.tranop.app.variance.common.spec.window

import no.ruter.tranop.app.common.time.DateTimeRange
import no.ruter.tranop.operation.common.dto.model.spec.DTOStopPointSpec
import no.ruter.tranop.operation.common.dto.model.spec.DTOStopPointSpecWindow

class StopPointSpecWindow(
    spec: DTOStopPointSpec?,
    source: DTOStopPointSpecWindow,
    window: DateTimeRange?,
) : SpecWindow<DTOStopPointSpec, DTOStopPointSpecWindow>(spec, source, window)
