package no.ruter.tranop.app.variance.mitigation.api.output

import no.ruter.tranop.app.common.insight.InsightContext
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.mapList
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.variance.common.api.adt.v4.input.ADTv4InputContext
import no.ruter.tranop.app.variance.common.api.adt.v4.output.AbstractADTv4APIOutputMapper
import no.ruter.tranop.app.variance.mitigation.db.InternalServiceMitigation
import no.ruter.tranop.assignment.adt.v4.model.APIMetadataEntry
import no.ruter.tranop.assignment.adt.v4.model.APIServiceImpact
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigation
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationLifeCycleInfo
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationParameters
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationSpec
import no.ruter.tranop.assignment.adt.v4.model.value.APIMetadataKey
import no.ruter.tranop.assignment.util.toIsoString
import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigationParameters
import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigationSpec
import no.ruter.tranop.operation.common.dto.model.DTOServiceImpact
import no.ruter.tranop.operation.common.dto.model.metadata.DTOServiceVarianceMetadataEntry

class MitigationOutputMapper(
    timeService: TimeService,
    insightService: InsightService,
) : AbstractADTv4APIOutputMapper(timeService, insightService) {
    fun mapMitigation(
        input: InternalServiceMitigation,
        context: ADTv4InputContext<*>,
    ): APIServiceMitigation {
        val data = input.data
        return APIServiceMitigation(
            spec = data.spec?.let { mapSpec(it, context) },
            lifecycle = mapLifeCycle(input),
        )
    }

    private fun mapSpec(
        input: DTOServiceMitigationSpec,
        context: ADTv4InputContext<*>,
    ): APIServiceMitigationSpec =
        APIServiceMitigationSpec(
            code = input.code?.value(),
            impact = input.impact?.let { mapImpact(it) },
            duration = input.duration?.let { mapDateTimeRange(it) },
            mitigates = input.mitigates.orEmpty(),
            metadata = context.mapList(input.metadata, path = "$.spec.metadata") { e, _, _ -> mapMetadataEntry(e) },
            parameters = input.parameters?.let { mapParameters(it) },
        )

    private fun mapImpact(input: DTOServiceImpact): APIServiceImpact =
        APIServiceImpact(
            journeys = input.journeys?.mapNotNull { mapJourneySpecWindowOption(it) },
        )

    private fun mapLifeCycle(input: InternalServiceMitigation): APIServiceMitigationLifeCycleInfo {
        val record = input.record
        return APIServiceMitigationLifeCycleInfo(
            created = record.createdAt?.toIsoString(),
            modified = record.modifiedAt?.toIsoString(),
            serviceMitigationId = record.ref,
        )
    }

    private fun mapParameters(input: DTOServiceMitigationParameters): APIServiceMitigationParameters =
        APIServiceMitigationParameters(
            vehicleId = input.vehicleId,
            transportMode = input.transportMode,
        )

    private fun mapMetadataEntry(input: DTOServiceVarianceMetadataEntry): APIMetadataEntry? =
        input.key?.value?.let { k ->
            APIMetadataEntry(
                key = APIMetadataKey.of(k)?.value,
                value = input.value,
            )
        }

    override val inputDesc: String
        get() = "api service mitigation" // TODO: Implement this.
    override val outputDesc: String
        get() = "dto service mitigation" // TODO: Implement this.

    override fun recordDataError(
        context: InsightContext,
        key: String,
        msg: String?,
        cause: Exception?,
    ) {
        // TODO: Implement this.
    }
}
