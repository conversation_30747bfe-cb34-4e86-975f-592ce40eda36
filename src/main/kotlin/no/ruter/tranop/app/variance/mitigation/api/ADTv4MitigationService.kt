package no.ruter.tranop.app.variance.mitigation.api

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.api.exception.APIException
import no.ruter.tranop.app.common.api.exception.APIResourceNotFoundException
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.variance.common.ServiceImpactContext
import no.ruter.tranop.app.variance.common.ServiceImpactValidator
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.common.api.adt.v4.input.ADTv4InputContext
import no.ruter.tranop.app.variance.mitigation.JourneyCancellationService
import no.ruter.tranop.app.variance.mitigation.JourneyReplacementService
import no.ruter.tranop.app.variance.mitigation.JourneyStandbyVehicleService
import no.ruter.tranop.app.variance.mitigation.ServiceMitigationHandler
import no.ruter.tranop.app.variance.mitigation.ServiceMitigationRequest
import no.ruter.tranop.app.variance.mitigation.api.input.MitigationInputMapper
import no.ruter.tranop.app.variance.mitigation.api.output.MitigationOutputMapper
import no.ruter.tranop.app.variance.mitigation.api.output.ReplacementServiceOutputMapper
import no.ruter.tranop.app.variance.mitigation.db.InternalServiceMitigation
import no.ruter.tranop.app.variance.mitigation.db.ServiceMitigationQueryBuilder
import no.ruter.tranop.app.variance.mitigation.db.ServiceMitigationRepository
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigation
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationPostRequest
import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigationSpec
import no.ruter.tranop.journey.mitigation.dto.value.DTOServiceMitigationCode
import org.apache.commons.lang3.NotImplementedException
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Isolation
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
class ADTv4MitigationService(
    val repo: ServiceMitigationRepository,
    private val standbyService: JourneyStandbyVehicleService,
    private val replacementService: JourneyReplacementService,
    private val cancellationService: JourneyCancellationService,
    insightService: InsightService,
    timeService: TimeService,
) {
    private val channel = repo.recordType.channels.input // TODO: Add API channel(s) for ADT v4, etc.?
    private val inputMapper = MitigationInputMapper(timeService, insightService)
    private val impactValidator = ServiceImpactValidator()

    // TODO: Merge output mappers?
    private val outputMapper = MitigationOutputMapper(timeService, insightService)
    private val replacementOutputMapper = ReplacementServiceOutputMapper()

    private val handlers: Map<DTOServiceMitigationCode, ServiceMitigationHandler> =
        mapOf(
            DTOServiceMitigationCode.CANCELLATION to cancellationService,
            DTOServiceMitigationCode.REPLACEMENT_SERVICE to replacementService,
            DTOServiceMitigationCode.STANDBY_VEHICLE_PLANNED to standbyService,
        )

    private fun validateInput(
        context: ADTv4InputContext<APIServiceMitigationPostRequest>,
    ): Pair<DTOServiceMitigationSpec, ServiceImpactContext> {
        val input = inputMapper.mapInput(context)
        val impactContext = impactValidator.validateImpact(repo.recordType, impact = input.impact, duration = input.duration)
        context.mergeServiceImpactValidationContext(impactContext)
        if (context.hasErrors) {
            throw IllegalArgumentException("Invalid service mitigation request")
        }
        return Pair(input, impactContext)
    }

    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT)
    fun createServiceMitigation(context: ADTv4InputContext<APIServiceMitigationPostRequest>): APIServiceMitigation {
        val type = ServiceVarianceRequest.Type.CREATE
        val (input, impact) = validateInput(context)
        val record = repo.newRecord(input)
        repo.storeRecord(record, trace = context.trace, now = context.received)
        val response = outputMapper.mapMitigation(record, context)
        return handleMitigationRequest(type, input, context, impact, record, response) ?: response
    }

    fun updateServiceMitigation(
        context: ADTv4InputContext<APIServiceMitigationPostRequest>,
        serviceMitigationId: String,
    ): APIServiceMitigation {
        val type = ServiceVarianceRequest.Type.UPDATE
        val stored =
            repo.fetchByRef(ref = serviceMitigationId, trace = context.trace)
                ?: throw APIResourceNotFoundException()

        if (stored.data.spec?.code == DTOServiceMitigationCode.CANCELLATION) {
            throw NotImplementedException("update for cancellations not yet implemented")
        }

        val (spec, impact) = validateInput(context)
        val modified = repo.wrapSpec(ref = serviceMitigationId, spec = spec, record = stored.record)
        repo.storeRecord(modified, trace = context.trace, now = context.received)
        val response = outputMapper.mapMitigation(modified, context)
        return handleMitigationRequest(type, spec, context, impact, stored, response) ?: response
    }

    fun deleteServiceMitigation(
        context: ADTv4InputContext<APIServiceMitigationPostRequest>,
        serviceMitigationId: String,
    ) {
        val type = ServiceVarianceRequest.Type.DELETE
        val stored =
            repo.fetchByRef(
                ref = serviceMitigationId,
                trace = context.trace,
                includeDeleted = true,
            ) ?: throw APIResourceNotFoundException()

        if (stored.deleted) {
            return // Record already deleted, no need to do anything.
        }

        val deleted = repo.setDeleted(ref = stored.record.ref, trace = context.trace, now = context.received)
        if (!deleted) {
            throw APIException(
                httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
                msg = "Error deleting mitigation $serviceMitigationId: record not found?",
            )
        }

        val spec = stored.data.spec
        val impact = // Resolve impact of original mitigation, so we can give the handler a chance to revert mitigation hen deleted.
            impactValidator.validateImpact(
                type = RecordType.SERVICE_MITIGATION,
                impact = spec.impact,
                duration = spec.duration,
            )
        handleMitigationRequest(type, spec, context, impact, stored, apiResponse = null)
    }

    private fun handleMitigationRequest(
        type: ServiceVarianceRequest.Type,
        input: DTOServiceMitigationSpec,
        context: ADTv4InputContext<APIServiceMitigationPostRequest>,
        impact: ServiceImpactContext,
        mitigation: InternalServiceMitigation,
        apiResponse: APIServiceMitigation? = null,
    ): APIServiceMitigation? =
        handlers[input.code]?.let { handler ->
            val request =
                ServiceMitigationRequest(
                    type = type,
                    trace = context.trace,
                    impact = impact,
                    channel = channel,
                    mitigation = mitigation,
                )
            val replacements = handler.handleMitigationRequest(request)
            if (replacements.isEmpty()) {
                apiResponse
            } else {
                apiResponse?.copy(
                    replacements = replacements.map(replacementOutputMapper::mapReplacement),
                )
            }
        } ?: apiResponse

    fun readServiceMitigation(
        context: ADTv4InputContext<*>,
        serviceMitigationId: String,
    ): APIServiceMitigation? {
        val mitigation = repo.fetchByRef(ref = serviceMitigationId, trace = context.trace)
        return mitigation?.let { outputMapper.mapMitigation(it, context) }
    }

    fun findServiceMitigations(
        context: ADTv4InputContext<*>,
        query: ServiceMitigationQueryBuilder,
    ): List<APIServiceMitigation> =
        repo
            .fetch(query)
            .map { outputMapper.mapMitigation(it, context) }
}
