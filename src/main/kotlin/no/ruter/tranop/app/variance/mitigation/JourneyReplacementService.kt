package no.ruter.tranop.app.variance.mitigation

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.mapping.deepCopy
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.journey.db.DatedJourneyRepository
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputService
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.app.variance.common.JourneyImpactService
import no.ruter.tranop.app.variance.common.ServiceImpactContext
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.common.spec.target.JourneyTarget
import no.ruter.tranop.app.variance.mitigation.db.InternalServiceMitigation
import no.ruter.tranop.app.variance.mitigation.replacement.BusForTramReplacementService
import no.ruter.tranop.app.variance.mitigation.replacement.ReplacementStopPointService
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOOperator
import no.ruter.tranop.dated.journey.dto.model.common.DTOOperatorContract
import no.ruter.tranop.dated.journey.dto.model.common.DTOTransportMode
import org.apache.commons.lang3.NotImplementedException
import org.springframework.stereotype.Service

@Service
class JourneyReplacementService(
    infoProperties: AppInfoProperties,
    stopPointRepo: StopPointRepository,
    private val timeService: TimeService,
    private val journeyRepo: DatedJourneyRepository,
    private val impactService: JourneyImpactService,
    private val cancellationService: JourneyCancellationService,
    private val datedJourneyInputService: DatedJourneyInputService,
) : ServiceMitigationHandler {
    private val busForTramReplacementService =
        BusForTramReplacementService(
            timeService = timeService,
            infoProperties = infoProperties,
            replacementStopPointService = ReplacementStopPointService(stopPointRepo, timeService, infoProperties),
        )

    private val defaultStandbyOperator = DTOOperator("RUT:Operator:140", "Vy Buss AS")

    // TODO: Does standbyOperator have an operatorContract for replacement journeys? As of now, standbyOperator is applied to tram operator contract
    private val defaultStandbyOperatorContract = DTOOperatorContract("RUT:OperatorContract:x4", "Oslo_Trikken", defaultStandbyOperator)

    override fun handleMitigationRequest(request: ServiceMitigationRequest): List<JourneyReplacements> =
        if (request.type == ServiceVarianceRequest.Type.CREATE) {
            performReplacement(request.trace, request.impact, request.channel, request.mitigation)
        } else {
            emptyList() // TODO: Implement support for UPDATE and DELETE.
        }

    private fun performReplacement(
        trace: TraceInfo,
        impact: ServiceImpactContext,
        channel: DataChannel,
        mitigation: InternalServiceMitigation,
    ): List<JourneyReplacements> {
        val targets = impactService.findJourneyTargets(impact)
        val plannedRecord =
            targets.journeys.lastOrNull()?.let {
                journeyRepo.fetchByRef(it.ref)
            } ?: throw IllegalArgumentException("Impacted journey not found")
        val plannedJourney = plannedRecord.journey

        if (plannedJourney.line?.transportMode == DTOTransportMode.TRAM) {
            val replacementJourney = busForTramReplacementService.createReplacementJourney(plannedJourney, trace)
            replacementJourney.operators = listOf(defaultStandbyOperator.deepCopy()) // TODO: should capture from input?
            replacementJourney.operatorContracts = listOf(defaultStandbyOperatorContract.deepCopy()) // TODO: capture from input?

            // TODO: Support draft mode (do not commit changes)
            // TODO: Support asynchronous update (do not commit changes on initial call, but do so in scheduled task later)
            addJourney(replacementJourney)
            cancellationService.cancelJourney(
                trace = trace,
                channel = channel,
                target = JourneyTarget(ref = plannedJourney.ref, calls = null), // full cancellation of planned journey.
                mitigation = mitigation,
                immediate = true,
            )

            val replacements =
                JourneyReplacements(
                    replaced = listOf(plannedJourney),
                    replacements = listOf(replacementJourney),
                )
            return listOf(replacements)
        } else {
            val msg = "Replacement for transport mode ${plannedJourney.line?.transportMode} not supported"
            throw NotImplementedException(msg)
        }
    }

    private fun addJourney(journey: DTODatedJourney) {
        datedJourneyInputService.processInternal(journey.ref, journey) // TODO validate
    }
}
