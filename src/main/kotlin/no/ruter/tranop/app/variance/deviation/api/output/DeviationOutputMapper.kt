package no.ruter.tranop.app.variance.deviation.api.output

import no.ruter.tranop.app.common.insight.InsightContext
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.mapList
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.variance.common.api.adt.v4.input.ADTv4InputContext
import no.ruter.tranop.app.variance.common.api.adt.v4.output.AbstractADTv4APIOutputMapper
import no.ruter.tranop.app.variance.deviation.db.InternalServiceDeviation
import no.ruter.tranop.assignment.adt.v4.model.APIMetadataEntry
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviation
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationLifeCycleInfo
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationParameters
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationReason
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationSpec
import no.ruter.tranop.assignment.adt.v4.model.APIServiceImpact
import no.ruter.tranop.assignment.adt.v4.model.value.APIMetadataKey
import no.ruter.tranop.assignment.util.toIsoString
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviationParameters
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviationSpec
import no.ruter.tranop.journey.deviation.dto.model.common.DTOServiceDeviationReason
import no.ruter.tranop.operation.common.dto.model.DTOServiceImpact
import no.ruter.tranop.operation.common.dto.model.metadata.DTOServiceVarianceMetadataEntry

class DeviationOutputMapper(
    timeService: TimeService,
    insightService: InsightService,
) : AbstractADTv4APIOutputMapper(timeService, insightService) {
    fun mapDeviation(
        input: InternalServiceDeviation,
        context: ADTv4InputContext<*>,
    ): APIServiceDeviation {
        val data = input.data
        return APIServiceDeviation(
            spec = data.spec?.let { mapSpec(it, context) },
            lifecycle = mapLifeCycle(input),
        )
    }

    private fun mapSpec(
        input: DTOServiceDeviationSpec,
        context: ADTv4InputContext<*>,
    ): APIServiceDeviationSpec =
        APIServiceDeviationSpec(
            code = input.code?.value(),
            reason = input.reason?.let { mapReason(it) },
            impact = input.impact?.let { mapImpact(it) },
            duration = input.duration?.let { mapDateTimeRange(it) },
            metadata = context.mapList(input.metadata, path = "$.spec.metadata") { e, _, _ -> mapMetadataEntry(e) },
            parameters = input.parameters?.let { mapParameters(it) },
        )

    private fun mapImpact(input: DTOServiceImpact): APIServiceImpact =
        APIServiceImpact(
            lines = input.lines?.mapNotNull { mapLineSpecWindow(it) },
            journeys = input.journeys?.mapNotNull { mapJourneySpecWindowOption(it) },
            stopPoints = input.stopPoints?.mapNotNull { mapWindowedStopPointSpec(it) },
        )

    private fun mapReason(input: DTOServiceDeviationReason): APIServiceDeviationReason =
        APIServiceDeviationReason(
            code = input.code,
            comment = input.comment,
        )

    private fun mapLifeCycle(input: InternalServiceDeviation): APIServiceDeviationLifeCycleInfo {
        val record = input.record
        return APIServiceDeviationLifeCycleInfo(
            created = record.createdAt?.toIsoString(),
            modified = record.modifiedAt?.toIsoString(),
            serviceDeviationId = record.ref,
        )
    }

    private fun mapParameters(input: DTOServiceDeviationParameters): APIServiceDeviationParameters =
        APIServiceDeviationParameters(
            vehicleId = input.vehicleId,
            delayMinutes = input.delayMinutes,
            operatorExempt = input.operatorExempt,
        )

    private fun mapMetadataEntry(input: DTOServiceVarianceMetadataEntry): APIMetadataEntry? =
        input.key?.value?.let { k ->
            APIMetadataEntry(
                key = APIMetadataKey.of(k)?.value,
                value = input.value,
            )
        }

    override val inputDesc: String
        get() = "api service deviation" // TODO: Implement this.
    override val outputDesc: String
        get() = "dto service deviation" // TODO: Implement this.

    override fun recordDataError(
        context: InsightContext,
        key: String,
        msg: String?,
        cause: Exception?,
    ) {
        // TODO: Implement this.
    }
}
