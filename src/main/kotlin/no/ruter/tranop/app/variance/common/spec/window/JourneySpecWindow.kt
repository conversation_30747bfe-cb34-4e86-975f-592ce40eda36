package no.ruter.tranop.app.variance.common.spec.window

import no.ruter.tranop.app.common.time.DateTimeRange
import no.ruter.tranop.app.variance.common.spec.JourneySpec
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneySpecWindow

class JourneySpecWindow(
    spec: JourneySpec?,
    source: DTOJourneySpecWindow,
    window: DateTimeRange?,
) : SpecWindow<JourneySpec, DTOJourneySpecWindow>(spec, source, window)
