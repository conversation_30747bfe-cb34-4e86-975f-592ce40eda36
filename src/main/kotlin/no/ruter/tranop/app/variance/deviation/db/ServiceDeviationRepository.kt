package no.ruter.tranop.app.variance.deviation.db

import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.db.record.base.BaseRecordRepository
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.assignmentmanager.db.sql.tables.DeviationTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DeviationRecord
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviationSpec
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class ServiceDeviationRepository(
    dslContext: DSLContext,
    timeService: TimeService,
    insightService: InsightService,
) : BaseRecordRepository<
        DeviationRecord,
        DeviationTable,
        InternalServiceDeviation,
        ServiceDeviationRecordMapper,
    >(
        recordType = TYPE,
        dslContext = dslContext,
        sortFields = ORDER_FIELDS,
        timeService = timeService,
        insightService = insightService,
        recordMapper = ServiceDeviationRecordMapper(insightService),
    ) {
    companion object {
        val TYPE = ServiceDeviationQueryBuilder.TYPE
        val TABLE = TYPE.table
        val ORDER_FIELDS =
            listOf(
                TABLE.CREATED_AT.asc(),
                TABLE.MODIFIED_AT.asc(),
                TABLE.ID.asc(),
            )
    }

    fun queryBuilder() = ServiceDeviationQueryBuilder()

    fun newRecord(spec: DTOServiceDeviationSpec): InternalServiceDeviation {
        val ref = MapperUtils.randomId(prefix = "sd-")
        val record = dslContext.newRecord(table)
        return wrapSpec(ref, spec, record)
    }

    fun wrapSpec(
        ref: String,
        spec: DTOServiceDeviationSpec,
        record: DeviationRecord,
    ): InternalServiceDeviation {
        val wrapper =
            DTOServiceDeviation().apply {
                this.ref = ref
                this.spec = spec
            }
        return InternalServiceDeviation(wrapper, record)
    }

    fun storeRecord(
        data: InternalServiceDeviation,
        trace: TraceInfo,
        now: OffsetDateTime? = null,
    ): Boolean {
        val t = now ?: timeService.now()
        val record = data.record
        recordMapper.updateRecord(source = data.data, record = record, trace = trace, now = t)
        return record.store() == 1
    }

    fun fetchByRef(
        ref: String?,
        trace: TraceInfo? = null,
        restricted: Boolean = true,
        includeDeleted: Boolean = !restricted,
    ): InternalServiceDeviation? {
        val b = queryBuilder().ref(ref).authorized(trace, restricted, includeDeleted)
        return fetch(b).firstOrNull()
    }
}
