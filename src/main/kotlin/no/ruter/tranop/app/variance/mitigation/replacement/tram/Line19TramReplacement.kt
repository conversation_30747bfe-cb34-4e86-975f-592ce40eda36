package no.ruter.tranop.app.variance.mitigation.replacement.tram

import no.ruter.tranop.app.common.api.exception.APIException
import no.ruter.tranop.app.variance.mitigation.replacement.QuayRef
import no.ruter.tranop.app.variance.mitigation.replacement.ReplacementStopCall
import no.ruter.tranop.dated.journey.dto.model.common.DTODatedJourneyDirectionCode
import org.springframework.http.HttpStatus

class Line19TramReplacement : TramLineReplacement {
    override fun getReplacementLineRef() = "RUT:Line:1902"

    override fun getReplacementStopCalls(directionCode: DTODatedJourneyDirectionCode): Map<QuayRef, ReplacementStopCall?> =
        stopMappings[directionCode]
            ?: throw APIException(HttpStatus.INTERNAL_SERVER_ERROR, "Invalid direction code on journey: $directionCode")

    private val stopMappings =
        mapOf(
            // inbound, direction = 1 // Majorstuen -> L<PERSON><PERSON>ru
            DTODatedJourneyDirectionCode.INBOUND to
                mapOf(
                    "NSR:Quay:104414" to ReplacementStopCall("NSR:Quay:104342", "301020103", "Majorstuen"),
                    "NSR:Quay:8149" to ReplacementStopCall("NSR:Quay:104052", "301021703", "Bogstadveien"),
                    "NSR:Quay:8119" to ReplacementStopCall("NSR:Quay:104061", "301021203", "Homansbyen"),
                    "NSR:Quay:7557" to ReplacementStopCall("NSR:Quay:104058", "301005705", "Holbergs plass"),
                    "NSR:Quay:7540" to ReplacementStopCall("NSR:Quay:102839", "301005503", "Tullinløkka"),
                    "NSR:Quay:7529" to ReplacementStopCall("NSR:Quay:101888", "301005303", "Tinghuset"),
                    "NSR:Quay:7497" to ReplacementStopCall("NSR:Quay:106026", "301005003", "Stortorvet"),
                    // Jernbanetorget plattform A
                    "NSR:Quay:7210" to ReplacementStopCall("NSR:Quay:7203", "301001302", "Jernbanetorget"),
                    "NSR:Quay:7167" to ReplacementStopCall("NSR:Quay:7169", "301000601", "Bjørvika"),
                    "NSR:Quay:104526" to ReplacementStopCall("NSR:Quay:104410", "301000401", "Middelalderparken"),
                    "NSR:Quay:12065" to ReplacementStopCall("NSR:Quay:106028", "301063003", "Oslo Hospital"),
                    "NSR:Quay:12139" to ReplacementStopCall("NSR:Quay:12145", "301071101", "Ekebergparken"),
                    "NSR:Quay:12190" to ReplacementStopCall("NSR:Quay:12195", "301073101", "Sportsplassen"),
                    "NSR:Quay:12219" to ReplacementStopCall("NSR:Quay:12215", "301073901", "Holtet"),
                    "NSR:Quay:12316" to ReplacementStopCall("NSR:Quay:12321", "301083101", "Sørli"),
                    "NSR:Quay:12322" to ReplacementStopCall("NSR:Quay:12325", "301084101", "Kastellet"),
                    "NSR:Quay:12332" to ReplacementStopCall("NSR:Quay:12339", "301085201", "Bråten"),
                    "NSR:Quay:12348" to ReplacementStopCall("NSR:Quay:12366", "301086721", "Sæter"),
                    "NSR:Quay:12369" to ReplacementStopCall("NSR:Quay:12371", "301087101", "Ljabru"),
                ),
            // outbound, direction = 2 // Ljabru -> Majorstuen
            DTODatedJourneyDirectionCode.OUTBOUND to
                mapOf(
                    "NSR:Quay:12368" to ReplacementStopCall("NSR:Quay:12370", "301087102", "Ljabru"),
                    "NSR:Quay:12349" to ReplacementStopCall("NSR:Quay:12367", "301086722", "Sæter"),
                    "NSR:Quay:12333" to ReplacementStopCall("NSR:Quay:12338", "301085202", "Bråten"),
                    "NSR:Quay:12323" to ReplacementStopCall("NSR:Quay:12324", "301084102", "Kastellet"),
                    "NSR:Quay:12317" to ReplacementStopCall("NSR:Quay:12320", "301083102", "Sørli"),
                    "NSR:Quay:12220" to ReplacementStopCall("NSR:Quay:12216", "301073902", "Holtet"),
                    "NSR:Quay:12191" to ReplacementStopCall("NSR:Quay:12193", "301073102", "Sportsplassen"),
                    // Jomfrubråten betjenes ikke
                    "NSR:Quay:12152" to null,
                    "NSR:Quay:12138" to ReplacementStopCall("NSR:Quay:12144", "301071102", "Ekebergparken"),
                    "NSR:Quay:12066" to ReplacementStopCall("NSR:Quay:106029", "301063004", "Oslo Hospital"),
                    "NSR:Quay:104525" to ReplacementStopCall("NSR:Quay:104409", "301000402", "Middelalderparken"),
                    "NSR:Quay:7168" to ReplacementStopCall("NSR:Quay:7170", "301000602", "Bjørvika"),
                    // Jernbanetorget plattform D
                    "NSR:Quay:7209" to ReplacementStopCall("NSR:Quay:7202", "301001301", "Jernbanetorget"),
                    "NSR:Quay:100634" to ReplacementStopCall("NSR:Quay:101886", "301005004", "Stortorvet"),
                    "NSR:Quay:7530" to ReplacementStopCall("NSR:Quay:101887", "301005304", "Tinghuset"),
                    "NSR:Quay:7539" to ReplacementStopCall("NSR:Quay:102838", "301005504", "Tullinløkka"),
                    "NSR:Quay:7556" to ReplacementStopCall("NSR:Quay:104059", "301005706", "Holbergs plass"),
                    "NSR:Quay:11484" to ReplacementStopCall("NSR:Quay:104062", "301030704", "Frydenlund"),
                    "NSR:Quay:101335" to ReplacementStopCall("NSR:Quay:101336", "301021104", "Welhavens gate"),
                    "NSR:Quay:8120" to ReplacementStopCall("NSR:Quay:104060", "301021204", "Homansbyen"),
                    "NSR:Quay:8150" to ReplacementStopCall("NSR:Quay:104053", "301021704", "Bogstadveien"),
                    "NSR:Quay:104413" to ReplacementStopCall("NSR:Quay:102319", "301020503", "Majorstuen"),
                ),
        )
}
