package no.ruter.tranop.app.variance.common.api.adt.v4.output

import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.AbstractMessageMapper
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.assignment.adt.v4.model.APIDateTimeRange
import no.ruter.tranop.assignment.adt.v4.model.APIJourneyCallSpec
import no.ruter.tranop.assignment.adt.v4.model.APIJourneyLineSpec
import no.ruter.tranop.assignment.adt.v4.model.APIJourneyLineSpecWindow
import no.ruter.tranop.assignment.adt.v4.model.APIJourneySpec
import no.ruter.tranop.assignment.adt.v4.model.APIJourneySpecWindow
import no.ruter.tranop.assignment.adt.v4.model.APIJourneySpecWindowOption
import no.ruter.tranop.assignment.adt.v4.model.APIStopPointSpec
import no.ruter.tranop.assignment.adt.v4.model.APIStopPointSpecWindow
import no.ruter.tranop.common.dto.model.DTODateTimeRange
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneyCallSpec
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneyLineSpec
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneyLineSpecWindow
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneySpec
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneySpecWindow
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneySpecWindowOption
import no.ruter.tranop.operation.common.dto.model.spec.DTOStopPointSpec
import no.ruter.tranop.operation.common.dto.model.spec.DTOStopPointSpecWindow

abstract class AbstractADTv4APIOutputMapper(
    timeService: TimeService,
    insightService: InsightService,
) : AbstractMessageMapper(timeService, insightService) {
    protected fun mapLineSpecWindow(input: DTOJourneyLineSpecWindow): APIJourneyLineSpecWindow =
        APIJourneyLineSpecWindow(
            spec = input.spec?.let { mapLineSpec(it) },
            serviceWindow = input.serviceWindow?.let { mapDateTimeRange(it) },
        )

    protected fun mapLineSpec(input: DTOJourneyLineSpec): APIJourneyLineSpec =
        APIJourneyLineSpec(
            lineId = input.lineId,
            direction = input.direction?.value,
        )

    protected fun mapJourneySpecWindowOption(input: DTOJourneySpecWindowOption): APIJourneySpecWindowOption =
        APIJourneySpecWindowOption(
            calls = input.calls?.mapNotNull { mapJourneyCallSpec(it) },
            journey = input.journey?.let { mapJourneySpecWindow(it) },
        )

    protected fun mapJourneyCallSpec(input: DTOJourneyCallSpec): APIJourneyCallSpec =
        APIJourneyCallSpec(
            stopPoint = input.stopPoint?.let { mapStopPointSpec(it) },
            arrivalDateTime = input.arrivalDateTime,
            departureDateTime = input.departureDateTime,
        )

    protected fun mapJourneySpecWindow(input: DTOJourneySpecWindow): APIJourneySpecWindow =
        APIJourneySpecWindow(
            spec = input.spec?.let { mapJourneySpec(it) },
            serviceWindow = input.serviceWindow?.let { mapDateTimeRange(it) },
        )

    protected fun mapJourneySpec(input: DTOJourneySpec): APIJourneySpec =
        APIJourneySpec(
            lineId = input.lineId,
            journeyId = input.journeyId,
            firstDepartureDateTime = input.firstDepartureDateTime,
        )

    protected fun mapWindowedStopPointSpec(input: DTOStopPointSpecWindow): APIStopPointSpecWindow =
        APIStopPointSpecWindow(
            spec = input.spec?.let { mapStopPointSpec(it) },
            serviceWindow = input.serviceWindow?.let { mapDateTimeRange(it) },
        )

    protected fun mapStopPointSpec(input: DTOStopPointSpec): APIStopPointSpec =
        APIStopPointSpec(
            quayId = input.quayId,
            stopPointId = input.stopPointId,
        )

    protected fun mapDateTimeRange(input: DTODateTimeRange): APIDateTimeRange =
        APIDateTimeRange(
            start = input.start,
            end = input.end,
        )
}
