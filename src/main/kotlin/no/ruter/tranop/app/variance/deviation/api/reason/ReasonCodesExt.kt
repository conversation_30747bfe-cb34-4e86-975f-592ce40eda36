package no.ruter.tranop.app.variance.deviation.api.reason

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.variance.deviation.api.reason.ReasonCodeCategory.CANCELLATION
import no.ruter.tranop.app.variance.deviation.api.reason.ReasonCodeCategory.OTHER_EVENT
import no.ruter.tranop.app.variance.deviation.api.reason.ReasonCodeSubCategory.INCOMPLETE_SERVICE
import no.ruter.tranop.app.variance.deviation.api.reason.ReasonCodeSubCategory.REALTIME_PROBLEM
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationReasonCodeSpec
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationReasonGroupSpec
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode.BYPASS
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode.DELAY
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode.NO_SERVICE
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode.NO_SIGN_ON
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode.UNKNOWN
import java.security.MessageDigest
import no.ruter.tranop.app.variance.deviation.api.reason.ReasonCodeCategory.DELAY as REASON_CODE_DELAY

private val mapper = jacksonObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)

fun String.toReasonCodeList(): List<ReasonCode> = JsonUtils.toObject(mapper, this, Array<ReasonCode>::class.java).toList()

fun ReasonCode.toDeviationReasonCode(): APIServiceDeviationReasonCodeSpec =
    APIServiceDeviationReasonCodeSpec(
        code = code,
        title = display,
        description = null, // Traffic Portal API Reason codes does not have description for a code
        groupCode = toGroupCode(),
        validDeviationCodes = applicableEventCategorizations.map { it.toDeviationCode() }.distinct(),
    )

fun ReasonCode.toDeviationReasonGroup(): APIServiceDeviationReasonGroupSpec =
    APIServiceDeviationReasonGroupSpec(
        code = toGroupCode(),
        title = displayGroupHeading,
        description = null, // Traffic Portal API Reason codes does not have description for a group
    )

fun ReasonCode.toGroupCode(): String =
    MessageDigest
        .getInstance("MD5")
        .digest(displayGroupHeading.toByteArray())
        .joinToString("") { "%02x".format(it) }

fun EventCategorization.toDeviationCode(): APIServiceDeviationCode =
    when (toReasonCodeCategoryEnum()) {
        CANCELLATION -> NO_SERVICE
        REASON_CODE_DELAY -> DELAY
        OTHER_EVENT ->
            when (toReasonCodeSubcategoryEnum()) {
                REALTIME_PROBLEM -> NO_SIGN_ON
                INCOMPLETE_SERVICE -> BYPASS
                else -> UNKNOWN
            }
        else -> UNKNOWN
    }

fun EventCategorization.toReasonCodeCategoryEnum(): ReasonCodeCategory? = ReasonCodeCategory.valueOfOrNull(this.category)

fun EventCategorization.toReasonCodeSubcategoryEnum(): ReasonCodeSubCategory? = ReasonCodeSubCategory.valueOfOrNull(this.subcategory)
