package no.ruter.tranop.app.variance.deviation.api

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.api.exception.APIException
import no.ruter.tranop.app.common.api.exception.APIResourceNotFoundException
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.variance.common.ServiceImpactContext
import no.ruter.tranop.app.variance.common.ServiceImpactValidator
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.common.api.adt.v4.input.ADTv4InputContext
import no.ruter.tranop.app.variance.deviation.JourneyBypassService
import no.ruter.tranop.app.variance.deviation.JourneyDelayService
import no.ruter.tranop.app.variance.deviation.JourneyNoServiceService
import no.ruter.tranop.app.variance.deviation.JourneyNoSignOnService
import no.ruter.tranop.app.variance.deviation.ServiceDeviationHandler
import no.ruter.tranop.app.variance.deviation.ServiceDeviationRequest
import no.ruter.tranop.app.variance.deviation.api.input.DeviationInputMapper
import no.ruter.tranop.app.variance.deviation.api.output.DeviationOutputMapper
import no.ruter.tranop.app.variance.deviation.db.InternalServiceDeviation
import no.ruter.tranop.app.variance.deviation.db.ServiceDeviationQueryBuilder
import no.ruter.tranop.app.variance.deviation.db.ServiceDeviationRepository
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviation
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationPostRequest
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviationSpec
import no.ruter.tranop.journey.deviation.dto.value.DTOServiceDeviationCode
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service

@Service
class ADTv4DeviationService(
    val repo: ServiceDeviationRepository,
    timeService: TimeService,
    insightService: InsightService,
    delayService: JourneyDelayService,
    bypassService: JourneyBypassService,
    noSignOnService: JourneyNoSignOnService,
    noServiceService: JourneyNoServiceService,
) {
    private val channel = repo.recordType.channels.input // TODO: Add API channel(s) for ADT v4, etc.?

    private val inputMapper = DeviationInputMapper(timeService, insightService)
    private val impactValidator = ServiceImpactValidator()

    private val outputMapper = DeviationOutputMapper(timeService, insightService)

    private val handlers: Map<DTOServiceDeviationCode, ServiceDeviationHandler> =
        mapOf(
            DTOServiceDeviationCode.DELAY to delayService,
            DTOServiceDeviationCode.BYPASS to bypassService,
            DTOServiceDeviationCode.NO_SIGN_ON to noSignOnService,
            DTOServiceDeviationCode.NO_SERVICE to noServiceService,
        )

    private fun validateInput(
        context: ADTv4InputContext<APIServiceDeviationPostRequest>,
    ): Pair<DTOServiceDeviationSpec, ServiceImpactContext> {
        val input = inputMapper.mapInput(context)
        val impactContext = impactValidator.validateImpact(type = repo.recordType, impact = input.impact, duration = input.duration)
        context.mergeServiceImpactValidationContext(impactContext)
        if (context.hasErrors) {
            throw IllegalArgumentException("Invalid service deviation request")
        }
        return Pair(input, impactContext)
    }

    fun createServiceDeviation(context: ADTv4InputContext<APIServiceDeviationPostRequest>): APIServiceDeviation {
        val type = ServiceVarianceRequest.Type.CREATE
        val (input, impact) = validateInput(context)
        val record = repo.newRecord(input)
        repo.storeRecord(record, trace = context.trace, now = context.received)
        val response = outputMapper.mapDeviation(record, context)
        handleDeviationRequest(type, input, context, impact, record)
        return response
    }

    fun updateServiceDeviation(
        context: ADTv4InputContext<APIServiceDeviationPostRequest>,
        serviceDeviationId: String,
    ): APIServiceDeviation {
        val type = ServiceVarianceRequest.Type.UPDATE
        val stored =
            repo.fetchByRef(ref = serviceDeviationId, trace = context.trace) ?: throw APIResourceNotFoundException()

        val (spec, impact) = validateInput(context)
        val modified = repo.wrapSpec(ref = serviceDeviationId, spec = spec, record = stored.record)
        repo.storeRecord(modified, trace = context.trace, now = context.received)
        val response = outputMapper.mapDeviation(modified, context)
        handleDeviationRequest(type, spec, context, impact, stored)
        return response
    }

    fun deleteServiceDeviation(
        context: ADTv4InputContext<APIServiceDeviationPostRequest>,
        serviceDeviationId: String,
    ) {
        val type = ServiceVarianceRequest.Type.DELETE
        val stored =
            repo.fetchByRef(
                ref = serviceDeviationId,
                trace = context.trace,
                includeDeleted = true,
            ) ?: throw APIResourceNotFoundException()

        if (stored.deleted) {
            return // Record already deleted, no need to do anything.
        }

        val deleted = repo.setDeleted(ref = stored.record.ref, trace = context.trace, now = context.received)
        if (!deleted) {
            throw APIException(
                httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
                msg = "Error deleting deviation $serviceDeviationId: record not found?",
            )
        }

        val spec = stored.data.spec
        val impact = // Resolve impact of original deviation, so we can give the handler a chance to revert mitigation hen deleted.
            impactValidator.validateImpact(
                type = RecordType.SERVICE_DEVIATION,
                impact = spec.impact,
                duration = spec.duration,
            )
        handleDeviationRequest(type, spec, context, impact, stored)
    }

    private fun handleDeviationRequest(
        type: ServiceVarianceRequest.Type,
        input: DTOServiceDeviationSpec,
        context: ADTv4InputContext<APIServiceDeviationPostRequest>,
        impact: ServiceImpactContext,
        deviation: InternalServiceDeviation,
    ) {
        handlers[input.code]?.let { handler ->
            val request =
                ServiceDeviationRequest(
                    type = type,
                    trace = context.trace,
                    impact = impact,
                    channel = channel,
                    deviation = deviation,
                )
            handler.handleDeviationRequest(request)
        }
    }

    fun readServiceDeviation(
        context: ADTv4InputContext<*>,
        serviceDeviationId: String,
    ): APIServiceDeviation? {
        val deviation = repo.fetchByRef(ref = serviceDeviationId, trace = context.trace)
        return deviation?.let { outputMapper.mapDeviation(it, context) }
    }

    fun findServiceDeviations(
        context: ADTv4InputContext<*>,
        query: ServiceDeviationQueryBuilder,
    ): List<APIServiceDeviation> =
        repo
            .fetch(query)
            .map { outputMapper.mapDeviation(it, context) }
}
