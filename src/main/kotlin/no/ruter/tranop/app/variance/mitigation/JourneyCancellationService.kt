package no.ruter.tranop.app.variance.mitigation

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.event.journey.input.JourneyEventInputService
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.app.variance.common.JourneyImpactService
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.common.spec.target.JourneyTarget
import no.ruter.tranop.app.variance.mitigation.db.InternalServiceMitigation
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventCancellation
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventCancellationCall
import org.springframework.stereotype.Service

@Service
class JourneyCancellationService(
    val timeService: TimeService,
    val stopPointRepo: StopPointRepository,
    val impactService: JourneyImpactService,
    val journeyEventInputService: JourneyEventInputService,
) : ServiceMitigationHandler {
    class Targets(
        val quayRefs: Map<String, String>,
        val journeys: List<JourneyTarget>,
    )

    override fun handleMitigationRequest(request: ServiceMitigationRequest): List<JourneyReplacements> {
        val type = request.type
        if (type == ServiceVarianceRequest.Type.UPDATE) return emptyList() // TODO: Implement support for UPDATE.

        val targets = resolveCancellationTargets(request)
        val cancelled = type == ServiceVarianceRequest.Type.CREATE // DELETE -> cancelled = false
        targets.journeys.forEach { target ->
            cancelJourney(
                trace = request.trace,
                channel = request.channel,
                target = target,
                mitigation = request.mitigation,
                immediate = true,
                targets = targets,
                cancelled = cancelled,
            )
        }
        return emptyList()
    }

    fun cancelJourney(
        trace: TraceInfo,
        channel: DataChannel,
        target: JourneyTarget,
        mitigation: InternalServiceMitigation,
        immediate: Boolean,
        targets: Targets? = null,
        cancelled: Boolean = true,
    ) {
        val now = timeService.now()
        val header =
            DTOMessageHeader().apply {
                traceId = trace.traceId
                messageTimestamp = now.toString()
            }

        val calls =
            target.calls?.map { call ->
                val stop = call.stopPoint
                val quayId =
                    stop?.quayId ?: targets?.quayRefs[stop?.stopPointId]
                        ?: throw IllegalStateException("unable to resolve NSR quay id for call")
                DTOJourneyEventCancellationCall().apply {
                    this.time = (call.departureTime ?: call.arrivalTime)?.toString()
                    this.nsrQuayId = quayId
                }
            }
        val cancellation =
            DTOJourneyEventCancellation().apply {
                this.calls = calls
                this.partial = target.partial
                this.cancelled = cancelled
            }

        val event =
            DTOJourneyEvent().apply {
                this.header = header
                this.cancellation = cancellation
                this.entityDatedJourneyKeyV2Ref = target.ref
                this.entityServiceMitigationKeyV1Ref = mitigation.ref
            }
        journeyEventInputService.store(now, event, channel, process = immediate)
    }

    private fun resolveCancellationTargets(request: ServiceMitigationRequest): Targets {
        val targets = impactService.findJourneyTargets(request.impact)
        val combinedTargets = targets.getCombinedTargets()

        // Resolve quay id for any calls referring to stop points using stop point ref instead of quay id.
        val stopPointRefs = LinkedHashSet<String>()
        combinedTargets.forEach { target ->
            target.calls?.forEach { call ->
                call.stopPoint?.let { stopPoint ->
                    if (stopPoint.quayId == null) {
                        stopPoint.stopPointId?.let { stopPointRefs.add(it) }
                    }
                }
            }
        }
        val quayIds = stopPointRepo.resolveQuayIds(stopPointRefs)
        return Targets(quayRefs = quayIds, journeys = combinedTargets)
    }
}
