package no.ruter.tranop.app.variance.deviation

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.variance.common.ServiceImpactContext
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.deviation.db.InternalServiceDeviation

class ServiceDeviationRequest(
    type: Type,
    trace: TraceInfo,
    impact: ServiceImpactContext,
    channel: DataChannel,
    val deviation: InternalServiceDeviation,
) : ServiceVarianceRequest(type, trace, impact, channel)
