package no.ruter.tranop.app.variance.common

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.insight.InsightContext

abstract class ServiceVarianceRequest(
    val type: Type,
    val trace: TraceInfo,
    val impact: ServiceImpactContext,
    override val channel: DataChannel,
) : InsightContext {
    enum class Type {
        CREATE,
        UPDATE,
        DELETE,
    }

    override val traceId: String?
        get() = trace.traceId

    override val metrics: Map<String, List<String>>
        get() = emptyMap() // TODO: Implement this properly.
}
