package no.ruter.tranop.app.variance.common

import no.ruter.tranop.app.common.mapping.MappingDetails
import no.ruter.tranop.app.common.time.DateTimeRange
import no.ruter.tranop.app.variance.common.spec.window.JourneyLineSpecWindow
import no.ruter.tranop.app.variance.common.spec.window.JourneySpecWindowOption
import no.ruter.tranop.app.variance.common.spec.window.StopPointSpecWindow
import no.ruter.tranop.operation.common.dto.model.DTOServiceImpact

class ServiceImpactContext(
    val source: DTOServiceImpact?,
) {
    val path = "$" // for now, we treat the mapped context as its own root path, regardless of where data was mapped from.

    val valid: Boolean
        get() = allErrors.all { it.isEmpty() }

    var duration: DateTimeRange? = null

    val lines = ArrayList<JourneyLineSpecWindow>()
    val journeys = ArrayList<JourneySpecWindowOption>()
    val stopPoints = ArrayList<StopPointSpecWindow>()

    val errors = ArrayList<MappingDetails>()
    val callErrors = ArrayList<MappingDetails>()
    val lineErrors = ArrayList<MappingDetails>()
    val journeyErrors = ArrayList<MappingDetails>()
    val stopPointErrors = ArrayList<MappingDetails>()

    private val allErrors =
        listOf(
            errors,
            callErrors,
            lineErrors,
            journeyErrors,
            stopPointErrors,
        )

    fun summary(): String = allErrors.flatten().joinToString(separator = "\n")
}
