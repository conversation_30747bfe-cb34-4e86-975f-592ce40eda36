package no.ruter.tranop.app.variance.common.api.adt.v4.input

import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.AbstractMessageMapper
import no.ruter.tranop.app.common.mapping.MappingDetails
import no.ruter.tranop.app.common.mapping.mapList
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.assignment.adt.v4.model.APIDateTimeRange
import no.ruter.tranop.assignment.adt.v4.model.APIJourneyCallSpec
import no.ruter.tranop.assignment.adt.v4.model.APIJourneyLineSpec
import no.ruter.tranop.assignment.adt.v4.model.APIJourneyLineSpecWindow
import no.ruter.tranop.assignment.adt.v4.model.APIJourneySpec
import no.ruter.tranop.assignment.adt.v4.model.APIJourneySpecWindow
import no.ruter.tranop.assignment.adt.v4.model.APIJourneySpecWindowOption
import no.ruter.tranop.assignment.adt.v4.model.APIServiceImpact
import no.ruter.tranop.assignment.adt.v4.model.APIStopPointSpec
import no.ruter.tranop.assignment.adt.v4.model.APIStopPointSpecWindow
import no.ruter.tranop.assignment.adt.v4.model.value.APIJourneyDirectionCode
import no.ruter.tranop.common.dto.model.DTODateTimeRange
import no.ruter.tranop.operation.common.dto.model.DTOServiceImpact
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneyCallSpec
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneyLineSpec
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneyLineSpecWindow
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneySpec
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneySpecWindow
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneySpecWindowOption
import no.ruter.tranop.operation.common.dto.model.spec.DTOStopPointSpec
import no.ruter.tranop.operation.common.dto.model.spec.DTOStopPointSpecWindow
import no.ruter.tranop.operation.common.dto.value.DTOJourneyDirectionCode

abstract class AbstractADTv4APIInputMapper(
    timeService: TimeService,
    insightService: InsightService,
) : AbstractMessageMapper(timeService, insightService) {
    fun mapImpact(
        input: APIServiceImpact,
        path: String,
        context: ADTv4InputContext<*>,
    ): DTOServiceImpact =
        DTOServiceImpact().apply {
            this.lines = context.mapList(input.lines, path = "$path.lines", mapper = ::mapLineSpecWindow)
            this.journeys = context.mapList(input.journeys, path = "$path.journeys", mapper = ::mapJourneySpecWindowOption)
            this.stopPoints = context.mapList(input.stopPoints, path = "$path.stopPoints", mapper = ::mapStopPointSpecWindow)
        }

    protected fun mapLineSpecWindow(
        input: APIJourneyLineSpecWindow,
        path: String,
        context: ADTv4InputContext<*>,
    ): DTOJourneyLineSpecWindow =
        DTOJourneyLineSpecWindow().apply {
            this.spec = input.spec?.let { mapLineSpec(it, path = "$path.spec", context = context) }
            this.serviceWindow = input.serviceWindow?.let { mapDuration(it, path = "$path.serviceWindow", context = context) }
        }

    protected fun mapLineSpec(
        input: APIJourneyLineSpec,
        path: String,
        context: ADTv4InputContext<*>,
    ): DTOJourneyLineSpec {
        val apiDirection = input.direction?.let(APIJourneyDirectionCode::of)
        return DTOJourneyLineSpec().apply {
            this.lineId = input.lineId
            // TODO: Implement proper direction mapping. Do NOT assume APIJourneyDirectionCode == DTOJourneyDirectionCode!!
            this.direction = apiDirection?.let { DTOJourneyDirectionCode.of(it.value) }
        }
    }

    protected fun mapDuration(
        input: APIDateTimeRange,
        path: String,
        context: ADTv4InputContext<*>,
    ): DTODateTimeRange {
        val end =
            input.end.toOffsetDateTime(
                context = context,
                path = "$path.end",
                insight = "$path.end",
                errorType = MappingDetails.Type.ERROR,
            )
        val start =
            input.start.toOffsetDateTime(
                context = context,
                path = "$path.start",
                insight = "$path.start",
                errorType = MappingDetails.Type.ERROR,
            )
        return DTODateTimeRange().apply {
            this.end = end?.toString()
            this.start = start?.toString()
        }
    }

    protected fun mapJourneySpecWindowOption(
        input: APIJourneySpecWindowOption,
        path: String,
        context: ADTv4InputContext<*>,
    ): DTOJourneySpecWindowOption =
        DTOJourneySpecWindowOption().apply {
            this.calls = context.mapList(input.calls, path = "$path.calls", mapper = ::mapJourneyCall)
            this.journey = input.journey?.let { mapJourneySpecWindow(it, path = "$path.journey", context = context) }
        }

    protected fun mapJourneySpecWindow(
        input: APIJourneySpecWindow,
        path: String,
        context: ADTv4InputContext<*>,
    ): DTOJourneySpecWindow {
        val window = input.serviceWindow?.let { mapDuration(it, path = "$path.serviceWindow", context = context) }
        return DTOJourneySpecWindow().apply {
            this.spec = input.spec?.let { mapJourneySpec(it, path = "$path.spec", context = context) }
            this.serviceWindow = window
        }
    }

    protected fun mapJourneySpec(
        input: APIJourneySpec,
        path: String,
        context: ADTv4InputContext<*>,
    ): DTOJourneySpec =
        DTOJourneySpec().apply {
            this.lineId = input.lineId
            this.journeyId = input.journeyId
            this.firstDepartureDateTime = input.firstDepartureDateTime
        }

    protected fun mapJourneyCall(
        input: APIJourneyCallSpec,
        path: String,
        context: ADTv4InputContext<*>,
    ): DTOJourneyCallSpec =
        DTOJourneyCallSpec().apply {
            this.stopPoint = input.stopPoint?.let { mapStopPointSpec(it, path = "$path.stopPoint", context = context) }
            this.arrivalDateTime = input.arrivalDateTime
            this.departureDateTime = input.departureDateTime
        }

    protected fun mapStopPointSpecWindow(
        input: APIStopPointSpecWindow,
        path: String,
        context: ADTv4InputContext<*>,
    ): DTOStopPointSpecWindow =
        DTOStopPointSpecWindow().apply {
            this.spec = input.spec?.let { mapStopPointSpec(it, path = "$path.spec", context = context) }
            this.serviceWindow = input.serviceWindow?.let { mapDuration(it, path = "$path.serviceWindow", context = context) }
        }

    protected fun mapStopPointSpec(
        input: APIStopPointSpec,
        path: String,
        context: ADTv4InputContext<*>,
    ): DTOStopPointSpec =
        DTOStopPointSpec().apply {
            this.quayId = input.quayId
            this.stopPointId = input.stopPointId
        }
}
