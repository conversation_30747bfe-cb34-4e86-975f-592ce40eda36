package no.ruter.tranop.app.variance.mitigation.replacement.tram

import no.ruter.tranop.app.common.api.exception.APIException
import no.ruter.tranop.app.variance.mitigation.replacement.QuayRef
import no.ruter.tranop.app.variance.mitigation.replacement.ReplacementStopCall
import no.ruter.tranop.dated.journey.dto.model.common.DTODatedJourneyDirectionCode
import org.springframework.http.HttpStatus

class Line18TramReplacement : TramLineReplacement {
    override fun getReplacementLineRef() = "RUT:Line:1802"

    override fun getReplacementStopCalls(directionCode: DTODatedJourneyDirectionCode): Map<QuayRef, ReplacementStopCall?> =
        stopMappings[directionCode]
            ?: throw APIException(HttpStatus.INTERNAL_SERVER_ERROR, "Invalid direction code on journey: $directionCode")

    private val stopMappings =
        mapOf(
            // inbound, direction = 1. Rikshospitalet - <PERSON><PERSON><PERSON><PERSON>
            DTODatedJourneyDirectionCode.INBOUND to
                mapOf(
                    // Rikshospitalet (retning Skøyen)
                    "NSR:Quay:11500" to ReplacementStopCall("NSR:Quay:110567", "301232452", "Rikshospitalet"),
                    "NSR:Quay:11501" to ReplacementStopCall("NSR:Quay:110567", "301232452", "Rikshospitalet"),
                    "NSR:Quay:11489" to ReplacementStopCall("NSR:Quay:105730", "301232301", "Gaustadalléen"),
                    "NSR:Quay:11662" to ReplacementStopCall("NSR:Quay:105729", "301037101", "Forskningsparken T"),
                    // Universitetet Blindern (i Problemveien)
                    "NSR:Quay:11651" to ReplacementStopCall("NSR:Quay:109569", "301036601", "Universitetet Blindern"),
                    "NSR:Quay:11625" to ReplacementStopCall("NSR:Quay:105727", "301035005", "John Colletts plass"),
                    "NSR:Quay:11586" to ReplacementStopCall("NSR:Quay:11581", "301034101", "Ullevål sykehus"),
                    "NSR:Quay:11507" to ReplacementStopCall("NSR:Quay:11504", "301031401", "Adamstuen"),
                    "NSR:Quay:11499" to ReplacementStopCall("NSR:Quay:104063", "301031303", "Stensgata"),
                    "NSR:Quay:11492" to ReplacementStopCall("NSR:Quay:104067", "301031203", "Bislett"),
                    "NSR:Quay:11481" to ReplacementStopCall("NSR:Quay:104066", "301030603", "Dalsbergstien"),
                    "NSR:Quay:101335" to ReplacementStopCall("NSR:Quay:101336", "301021104", "Welhavens gate"),
                    "NSR:Quay:7557" to ReplacementStopCall("NSR:Quay:104058", "301005705", "Holbergs plass"),
                    "NSR:Quay:7540" to ReplacementStopCall("NSR:Quay:102839", "301005503", "Tullinløkka"),
                    "NSR:Quay:7529" to ReplacementStopCall("NSR:Quay:101888", "301005303", "Tinghuset"),
                    "NSR:Quay:7497" to ReplacementStopCall("NSR:Quay:106026", "301005003", "Stortorvet"),
                    // Jernbanetorget (Plf E - ved Europarådets plass)
                    "NSR:Quay:7175" to ReplacementStopCall("NSR:Quay:104022", "301000701", "Jernbanetorget"),
                    "NSR:Quay:105273" to ReplacementStopCall("NSR:Quay:105306", "301006403", "Storgata"),
                    "NSR:Quay:104871" to ReplacementStopCall("NSR:Quay:106761", "301051003", "Nybrua"),
                    "NSR:Quay:11821" to ReplacementStopCall("NSR:Quay:104041", "301051203", "Schous plass"),
                    "NSR:Quay:11825" to ReplacementStopCall("NSR:Quay:104035", "301051303", "Olaf Ryes plass"),
                    // Birkelunden i Schleppegrells gate (riktig quayRef?)
                    "NSR:Quay:11842" to ReplacementStopCall("NSR:Quay:11849", "301052003", "Birkelunden"),
                    "NSR:Quay:11867" to ReplacementStopCall("NSR:Quay:104021", "301052703", "Biermanns gate"),
                    "NSR:Quay:11728" to ReplacementStopCall("NSR:Quay:104018", "301043005", "Torshov"),
                    "NSR:Quay:11752" to ReplacementStopCall("NSR:Quay:101338", "301044005", "Sandaker senter"),
                    "NSR:Quay:11770" to ReplacementStopCall("NSR:Quay:104016", "301044303", "Grefsenveien"),
                    "NSR:Quay:11124" to ReplacementStopCall("NSR:Quay:104047", "301212105", "Storo"),
                    // Grefsen stasjon utelates (?)
                    "NSR:Quay:11105" to null,
                    "NSR:Quay:11104" to null,
                    "NSR:Quay:11103" to null,
                ),
            // outbound, direction = 2. Grefsen - Rikshospitalet
            DTODatedJourneyDirectionCode.OUTBOUND to
                mapOf(
                    // Grefsen stasjon -> utelates?
                    "NSR:Quay:11105" to null,
                    "NSR:Quay:11104" to null,
                    "NSR:Quay:11103" to null,
                    "NSR:Quay:11123" to ReplacementStopCall("NSR:Quay:104046", "301212106", "Storo"),
                    "NSR:Quay:11769" to ReplacementStopCall("NSR:Quay:104015", "301044304", "Grefsenveien"),
                    "NSR:Quay:11755" to ReplacementStopCall("NSR:Quay:101337", "301044006", "Sandaker senter"),
                    "NSR:Quay:11727" to ReplacementStopCall("NSR:Quay:104017", "301043006", "Torshov"),
                    "NSR:Quay:11866" to ReplacementStopCall("NSR:Quay:104020", "301052704", "Biermanns gate"),
                    // Birkelunden (Schleppegrell gt Retning sentrum)
                    "NSR:Quay:11843" to ReplacementStopCall("NSR:Quay:11848", "301052002", "Birkelunden"),
                    "NSR:Quay:11826" to ReplacementStopCall("NSR:Quay:104034", "301051304", "Olaf Ryes plass"),
                    "NSR:Quay:11820" to ReplacementStopCall("NSR:Quay:104040", "301051204", "Schous plass"),
                    "NSR:Quay:104872" to ReplacementStopCall("NSR:Quay:106762", "301051004", "Nybrua"),
                    "NSR:Quay:105274" to ReplacementStopCall("NSR:Quay:105305", "301006404", "Storgata"),
                    // Jernbanetorget (Plf F - ved Europarådets plass)
                    "NSR:Quay:7176" to ReplacementStopCall("NSR:Quay:104023", "301000702", "Jernbanetorget"),
                    "NSR:Quay:100634" to ReplacementStopCall("NSR:Quay:101886", "301005004", "Stortorvet"),
                    "NSR:Quay:7530" to ReplacementStopCall("NSR:Quay:101887", "301005304", "Tinghuset"),
                    "NSR:Quay:7539" to ReplacementStopCall("NSR:Quay:102838", "301005504", "Tullinløkka"),
                    "NSR:Quay:7556" to ReplacementStopCall("NSR:Quay:104059", "301005706", "Holbergs plass"),
                    "NSR:Quay:11484" to ReplacementStopCall("NSR:Quay:104062", "301030704", "Frydenlund"),
                    "NSR:Quay:11480" to ReplacementStopCall("NSR:Quay:104065", "301030604", "Dalsbergstien"),
                    "NSR:Quay:11494" to ReplacementStopCall("NSR:Quay:104068", "301031204", "Bislett"),
                    "NSR:Quay:11498" to ReplacementStopCall("NSR:Quay:104064", "301031304", "Stensgata"),
                    "NSR:Quay:11508" to ReplacementStopCall("NSR:Quay:11502", "301031402", "Adamstuen"),
                    "NSR:Quay:11587" to ReplacementStopCall("NSR:Quay:11583", "301034102", "Ullevål sykehus"),
                    "NSR:Quay:11626" to ReplacementStopCall("NSR:Quay:11620", "301035004", "John Colletts plass"),
                    // Universitetet Blindern (i Problemveien)
                    "NSR:Quay:11650" to ReplacementStopCall("NSR:Quay:109570", "301036602", "Universitetet Blindern"),
                    "NSR:Quay:11663" to ReplacementStopCall("NSR:Quay:105728", "301037102", "Forskningsparken T"),
                    "NSR:Quay:11490" to ReplacementStopCall("NSR:Quay:105731", "301232302", "Gaustadalléen"),
                    // Rikshospitalet (retning Øvre Sogn)
                    "NSR:Quay:11500" to ReplacementStopCall("NSR:Quay:110586", "301232453", "Rikshospitalet"),
                    "NSR:Quay:11501" to ReplacementStopCall("NSR:Quay:110586", "301232453", "Rikshospitalet"),
                ),
        )
}
