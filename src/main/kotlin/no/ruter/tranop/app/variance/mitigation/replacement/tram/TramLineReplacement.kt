package no.ruter.tranop.app.variance.mitigation.replacement.tram

import no.ruter.tranop.app.variance.mitigation.replacement.QuayRef
import no.ruter.tranop.app.variance.mitigation.replacement.ReplacementStopCall
import no.ruter.tranop.dated.journey.dto.model.common.DTODatedJourneyDirectionCode

interface TramLineReplacement {
    fun getReplacementLineRef(): String

    fun getReplacementStopCalls(directionCode: DTODatedJourneyDirectionCode): Map<QuayRef, ReplacementStopCall?>
}
