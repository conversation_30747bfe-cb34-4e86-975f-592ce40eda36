package no.ruter.tranop.app.variance.mitigation.replacement.tram

import no.ruter.tranop.app.common.api.exception.APIException
import no.ruter.tranop.app.variance.mitigation.replacement.QuayRef
import no.ruter.tranop.app.variance.mitigation.replacement.ReplacementStopCall
import no.ruter.tranop.dated.journey.dto.model.common.DTODatedJourneyDirectionCode
import org.springframework.http.HttpStatus

class Line15TramReplacement : TramLineReplacement {
    override fun getReplacementLineRef() = "RUT:Line:1599"

    override fun getReplacementStopCalls(directionCode: DTODatedJourneyDirectionCode): Map<QuayRef, ReplacementStopCall?> =
        stopMappings[directionCode]
            ?: throw APIException(HttpStatus.INTERNAL_SERVER_ERROR, "Invalid direction code on journey: $directionCode")

    // Aker brygge og R<PERSON>ø<PERSON> har blitt brukt ved planlagt avvik (Holmenkollstafetten)
    private val stopMappings =
        mapOf(
            // inbound, direction = 1
            DTODatedJourneyDirectionCode.INBOUND to
                mapOf(
                    // Majorstuen plattform B (i Bogstadveien)
                    "NSR:Quay:104413" to ReplacementStopCall("NSR:Quay:104343", "301020404", "Majorstuen"),
                    // Majorstuen plattform C
                    "NSR:Quay:8058" to ReplacementStopCall("NSR:Quay:102319", "301020503", "Majorstuen"),
                    "NSR:Quay:8201" to ReplacementStopCall("NSR:Quay:8209", "301022214", "Frogner stadion"),
                    "NSR:Quay:8189" to ReplacementStopCall("NSR:Quay:8182", "301022102", "Vigelandsparken"),
                    "NSR:Quay:8170" to ReplacementStopCall("NSR:Quay:104042", "301022003", "Frogner plass"),
                    "NSR:Quay:7805" to ReplacementStopCall("NSR:Quay:104038", "301013103", "Elisenberg"),
                    "NSR:Quay:7827" to ReplacementStopCall("NSR:Quay:104037", "301013403", "Lille Frogner allé"),
                    "NSR:Quay:7753" to ReplacementStopCall("NSR:Quay:104032", "301011403", "Niels Juels gate"),
                    // Solli plattform A
                    "NSR:Quay:7731" to ReplacementStopCall("NSR:Quay:104031", "301011003", "Solli"),
                    // Solli plattform C
                    "NSR:Quay:101227" to ReplacementStopCall("NSR:Quay:7739", "301011201", "Solli"),
                    "NSR:Quay:7796" to ReplacementStopCall("NSR:Quay:101229", "301012603", "Ruseløkka"),
                    // Aker brygge --> Nationaltheatret plattform D
                    "NSR:Quay:7434" to ReplacementStopCall("NSR:Quay:7349", "301003201", "Nationaltheatret"),
                    // Nationaltheatret plattform C -> plattform D
                    "NSR:Quay:7358" to ReplacementStopCall("NSR:Quay:7349", "301003201", "Nationaltheatret"),
                    "NSR:Quay:7279" to ReplacementStopCall("NSR:Quay:105725", "301002103", "Øvre Slottsgate"),
                    "NSR:Quay:7246" to ReplacementStopCall("NSR:Quay:105723", "301001905", "Dronningens gate"),
                    // Jernbanetorget (Plf C - buss for trikk, retning øst)
                    "NSR:Quay:7209" to ReplacementStopCall("NSR:Quay:100635", "301001313", "Jernbanetorget"),
                    "NSR:Quay:105273" to ReplacementStopCall("NSR:Quay:105306", "301006403", "Storgata"),
                    "NSR:Quay:104871" to ReplacementStopCall("NSR:Quay:106761", "301051003", "Nybrua"),
                    "NSR:Quay:11821" to ReplacementStopCall("NSR:Quay:104041", "301051203", "Schous plass"),
                    "NSR:Quay:11825" to ReplacementStopCall("NSR:Quay:104035", "301051303", "Olaf Ryes plass"),
                    // Birkelunden i Schleppegrells gate (riktig quayRef?)
                    "NSR:Quay:11842" to ReplacementStopCall("NSR:Quay:11849", "301052003", "Birkelunden"),
                    "NSR:Quay:11867" to ReplacementStopCall("NSR:Quay:104021", "301052703", "Biermanns gate"),
                    "NSR:Quay:11728" to ReplacementStopCall("NSR:Quay:104018", "301043005", "Torshov"),
                    "NSR:Quay:11752" to ReplacementStopCall("NSR:Quay:101338", "301044005", "Sandaker senter"),
                    "NSR:Quay:11770" to ReplacementStopCall("NSR:Quay:104016", "301044303", "Grefsenveien"),
                    "NSR:Quay:11124" to ReplacementStopCall("NSR:Quay:104047", "301212105", "Storo"),
                    "NSR:Quay:11170" to ReplacementStopCall("NSR:Quay:104014", "301214005", "Disen"),
                    "NSR:Quay:11192" to ReplacementStopCall("NSR:Quay:104012", "301214503", "Doktor Smiths vei"),
                    "NSR:Quay:11195" to ReplacementStopCall("NSR:Quay:104010", "301214603", "Glads vei"),
                    "NSR:Quay:11200" to ReplacementStopCall("NSR:Quay:104006", "301214703", "Grefsenplatået"),
                    "NSR:Quay:11262" to ReplacementStopCall("NSR:Quay:104008", "301216503", "Grefsen stadion"),
                    // Kjelsåsalléen, i Kjelsåsveien
                    "NSR:Quay:11247" to ReplacementStopCall("NSR:Quay:11252", "301216421", "Kjelsåsalléen"),
                    // Kjelsås --> Kjelsås stasjon
                    "NSR:Quay:11242" to ReplacementStopCall("NSR:Quay:11239", "301216106", "Kjelsås stasjon"),
                    // Grefsen stasjon utelates (?)
                    "NSR:Quay:101950" to null,
                    "NSR:Quay:11103" to null,
                ),
            // outbound, direction = 2
            DTODatedJourneyDirectionCode.OUTBOUND to
                mapOf(
                    // Kjelsås --> Kjelsås stasjon
                    "NSR:Quay:11244" to ReplacementStopCall("NSR:Quay:11238", "301216101", "Kjelsås stasjon"),
                    // riktig quay?
                    "NSR:Quay:11245" to ReplacementStopCall("NSR:Quay:11251", "301216422", "Kjelsåsalléen"),
                    "NSR:Quay:11259" to ReplacementStopCall("NSR:Quay:104007", "301216504", "Grefsen stadion"),
                    "NSR:Quay:11198" to ReplacementStopCall("NSR:Quay:104005", "301214704", "Grefsenplatået"),
                    "NSR:Quay:11196" to ReplacementStopCall("NSR:Quay:104009", "301214604", "Glads vei"),
                    "NSR:Quay:11193" to ReplacementStopCall("NSR:Quay:104011", "301214504", "Doktor Smiths vei"),
                    "NSR:Quay:11172" to ReplacementStopCall("NSR:Quay:104013", "301214006", "Disen"),
                    "NSR:Quay:11169" to ReplacementStopCall("NSR:Quay:104013", "301214006", "Disen"), // disen 2
                    "NSR:Quay:11123" to ReplacementStopCall("NSR:Quay:104046", "301212106", "Storo"),
                    "NSR:Quay:11769" to ReplacementStopCall("NSR:Quay:104015", "301044304", "Grefsenveien"),
                    "NSR:Quay:11755" to ReplacementStopCall("NSR:Quay:101337", "301044006", "Sandaker senter"),
                    "NSR:Quay:11727" to ReplacementStopCall("NSR:Quay:104017", "301043006", "Torshov"),
                    "NSR:Quay:11866" to ReplacementStopCall("NSR:Quay:104020", "301052704", "Biermanns gate"),
                    // Birkelunden (Schleppegrell gt Retning sentrum)
                    "NSR:Quay:11843" to ReplacementStopCall("NSR:Quay:11848", "301052002", "Birkelunden"),
                    "NSR:Quay:11826" to ReplacementStopCall("NSR:Quay:104034", "301051304", "Olaf Ryes plass"),
                    "NSR:Quay:11820" to ReplacementStopCall("NSR:Quay:104040", "301051204", "Schous plass"),
                    "NSR:Quay:104872" to ReplacementStopCall("NSR:Quay:106762", "301051004", "Nybrua"),
                    "NSR:Quay:105274" to ReplacementStopCall("NSR:Quay:105305", "301006404", "Storgata"),
                    // Jernbanetorget (Plf B - buss for trikk, retning vest)
                    "NSR:Quay:7210" to ReplacementStopCall("NSR:Quay:100636", "301001314", "Jernbanetorget"),
                    // Jernbanetorget (Plf F - ved Europarådets plass)
                    "NSR:Quay:7176" to ReplacementStopCall("NSR:Quay:104023", "301000702", "Jernbanetorget"),
                    "NSR:Quay:7245" to ReplacementStopCall("NSR:Quay:105722", "301001906", "Dronningens gate"),
                    "NSR:Quay:7277" to ReplacementStopCall("NSR:Quay:105724", "301002104", "Øvre Slottsgate"),
                    // Nationaltheatret plattform B -> plattform A
                    "NSR:Quay:7357" to ReplacementStopCall("NSR:Quay:7350", "301003202", "Nationaltheatret"),
                    // Aker brygge --> Nationaltheatret plattform A
                    "NSR:Quay:7433" to ReplacementStopCall("NSR:Quay:7350", "301003202", "Nationaltheatret"),
                    "NSR:Quay:7795" to ReplacementStopCall("NSR:Quay:101230", "301012604", "Ruseløkka"),
                    // Solli plattform B
                    "NSR:Quay:7732" to ReplacementStopCall("NSR:Quay:104030", "301011004", "Solli"),
                    // Solli plattform D
                    "NSR:Quay:101228" to ReplacementStopCall("NSR:Quay:7747", "301011202", "Solli"),
                    "NSR:Quay:7754" to ReplacementStopCall("NSR:Quay:104033", "301011404", "Niels Juels gate"),
                    "NSR:Quay:7828" to ReplacementStopCall("NSR:Quay:104036", "301013404", "Lille Frogner allé"),
                    "NSR:Quay:7804" to ReplacementStopCall("NSR:Quay:104039", "301013104", "Elisenberg"),
                    "NSR:Quay:8171" to ReplacementStopCall("NSR:Quay:104043", "301022004", "Frogner plass"),
                    "NSR:Quay:8190" to ReplacementStopCall("NSR:Quay:8181", "301022101", "Vigelandsparken"),
                    "NSR:Quay:8200" to ReplacementStopCall("NSR:Quay:8210", "301022213", "Frogner stadion"),
                    // Majorstuen plattform A (i Bogstadveien)
                    "NSR:Quay:104414" to ReplacementStopCall("NSR:Quay:104342", "301020103", "Majorstuen"),
                    // Majorstuen plattform C
                    "NSR:Quay:8058" to ReplacementStopCall("NSR:Quay:102319", "301020503", "Majorstuen"),
                    // Stortorvet
                    "NSR:Quay:100634" to ReplacementStopCall("NSR:Quay:101886", "301005004", "Stortorvet"),
                    // Grefsen stasjon utelates (?)
                    "NSR:Quay:101944" to null,
                    "NSR:Quay:101950" to null,
                ),
        )
}
