package no.ruter.tranop.app.variance.mitigation.db

import no.ruter.tranop.assignmentmanager.db.sql.tables.records.MitigationRecord
import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigation

class InternalServiceMitigation(
    val data: DTOServiceMitigation,
    val record: MitigationRecord,
) {
    val ref: String?
        get() = record.ref ?: data.ref

    val deleted: Boolean
        get() = record.deletedAt != null
}
