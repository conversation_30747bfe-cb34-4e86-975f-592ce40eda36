package no.ruter.tranop.app.variance.common.spec.target

import no.ruter.tranop.app.variance.common.ServiceImpactContext

class JourneyTargets(
    val context: ServiceImpactContext,
) {
    var journeys = ArrayList<JourneySpecWindowOptionTarget>()
    var lineJourneys = ArrayList<JourneyLineSpecWindowTarget>()
    var stopPointJourneys = ArrayList<StopPointSpecWindowTarget>()

    fun getCombinedTargets(): List<JourneyTarget> {
        val res = ArrayList<JourneyTarget>()
        journeys.forEach { journey ->
            val calls = journey.option.calls.mapNotNull { it.spec }
            res.add(JourneyTarget(journey.ref, calls))
        }
        lineJourneys.forEach { line ->
            line.refs.forEach { res.add(JourneyTarget(it)) }
        }
        stopPointJourneys.forEach { line ->
            line.refs.forEach { res.add(JourneyTarget(it)) }
        }
        return res
    }
}
