package no.ruter.tranop.app.variance.mitigation.api

import no.ruter.tranop.app.common.api.exception.APIResourceNotFoundException
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.variance.common.api.adt.v4.AbstractADTv4APIController
import no.ruter.tranop.app.variance.mitigation.db.ServiceMitigationQueryBuilder
import no.ruter.tranop.assignment.adt.v4.api.MitigationApi
import no.ruter.tranop.assignment.adt.v4.model.APIPageInfo
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationGetResponse
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationListResponse
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationPostRequest
import no.ruter.tranop.assignment.adt.v4.model.APIServiceMitigationPostResponse
import no.ruter.tranop.assignment.adt.v4.model.value.APIPostRequestType
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class ADTv4MitigationAPIController(
    timeService: TimeService,
    insightService: InsightService,
    private val mitigationService: ADTv4MitigationService,
) : AbstractADTv4APIController(
        channel = mitigationService.repo.recordType.channels.input,
        timeService = timeService,
        insightService = insightService,
    ),
    MitigationApi {
    override fun findServiceMitigations(
        xADTAuth: String?,
        xTraceId: String?,
        xRequestId: String?,
        xOperatorId: String?,
        xAuthorityId: String?,
        fromDateTime: String?,
        toDateTime: String?,
        limit: Int?,
        offset: Int?,
    ): ResponseEntity<APIServiceMitigationListResponse> {
        val context =
            createInputContext<Unit?>(
                request = null,
                ref = MapperUtils.randomId("sd-"),
                xADTAuth = xADTAuth,
                xTraceId = xTraceId,
                xRequestId = xRequestId,
                xOperatorId = xOperatorId,
                xAuthorityId = xAuthorityId,
            )

        val errorHandler: (HttpStatus, String?, Exception) -> ResponseEntity<APIServiceMitigationListResponse> =
            { s, m, _ -> apiResponseEntity(s, context, m) { r -> APIServiceMitigationListResponse(result = r) } }
        return execute(
            name = "mitigation.find",
            context = context,
            action =
                {
                    val query =
                        ServiceMitigationQueryBuilder()
                            .page(limit = limit, offset = offset)
                            .authorized(trace = context.trace, restricted = true, includeDeleted = false)
                            .toDateTime(toDateTime)
                            .fromDateTime(fromDateTime)
                    val mitigations = mitigationService.findServiceMitigations(context, query)
                    apiResponse(HttpStatus.OK, context) { r ->
                        val page = APIPageInfo(limit = limit, offset = offset, itemCount = mitigations.size)
                        APIServiceMitigationListResponse(result = r, items = mitigations, page = page)
                    }
                },
            badRequestHandler = errorHandler,
            internalErrorHandler = errorHandler,
        )
    }

    override fun getServiceMitigation(
        serviceMitigationId: String,
        xADTAuth: String?,
        xTraceId: String?,
        xRequestId: String?,
        xOperatorId: String?,
        xAuthorityId: String?,
    ): ResponseEntity<APIServiceMitigationGetResponse> {
        val context =
            createInputContext<Unit?>(
                request = null,
                ref = serviceMitigationId,
                xADTAuth = xADTAuth,
                xTraceId = xTraceId,
                xRequestId = xRequestId,
                xOperatorId = xOperatorId,
                xAuthorityId = xAuthorityId,
            )
        val errorHandler: (HttpStatus, String?, Exception) -> ResponseEntity<APIServiceMitigationGetResponse> =
            { s, m, _ -> apiResponseEntity(s, context, m) { r -> APIServiceMitigationGetResponse(result = r) } }
        return execute(
            name = "mitigation.read",
            context = context,
            action =
                {
                    val mitigation =
                        mitigationService.readServiceMitigation(context, serviceMitigationId)
                            ?: throw APIResourceNotFoundException()
                    apiResponse(HttpStatus.OK, context) { r ->
                        APIServiceMitigationGetResponse(result = r, mitigation = mitigation)
                    }
                },
            badRequestHandler = errorHandler,
            internalErrorHandler = errorHandler,
        )
    }

    override fun postServiceMitigation(
        apIServiceMitigationPostRequest: APIServiceMitigationPostRequest,
        xADTAuth: String?,
        xTraceId: String?,
        xRequestId: String?,
        xOperatorId: String?,
        xAuthorityId: String?,
    ): ResponseEntity<APIServiceMitigationPostResponse> {
        val action = apIServiceMitigationPostRequest.action?.let(APIPostRequestType::of) ?: APIPostRequestType.CREATE
        val draft = apIServiceMitigationPostRequest.draft ?: false
        val context =
            createInputContext(
                request = apIServiceMitigationPostRequest,
                ref = MapperUtils.randomId("sd-"),
                xADTAuth = xADTAuth,
                xTraceId = xTraceId,
                xRequestId = xRequestId,
                xOperatorId = xOperatorId,
                xAuthorityId = xAuthorityId,
            )
        val errorHandler: (HttpStatus, String?, Exception) -> ResponseEntity<APIServiceMitigationPostResponse> =
            { s, m, _ -> apiResponseEntity(s, context, m) { r -> APIServiceMitigationPostResponse(result = r) } }
        return execute(
            name = "mitigation.create",
            context = context,
            action =
                { ctx ->
                    if (action == APIPostRequestType.CREATE) {
                        // does not support draft=true yet
                        if (draft == true) {
                            apiResponse(HttpStatus.NOT_IMPLEMENTED, context) { r ->
                                APIServiceMitigationPostResponse(result = r)
                            }
                        } else {
                            val mitigation = mitigationService.createServiceMitigation(ctx)
                            apiResponse(HttpStatus.CREATED, context) { r ->
                                APIServiceMitigationPostResponse(result = r, mitigation = mitigation)
                            }
                        }
                    } else {
                        throw IllegalArgumentException("Invalid action: $action")
                    }
                },
            badRequestHandler = errorHandler,
            internalErrorHandler = errorHandler,
        )
    }

    override fun updateServiceMitigation(
        serviceMitigationId: String,
        apIServiceMitigationPostRequest: APIServiceMitigationPostRequest,
        xADTAuth: String?,
        xTraceId: String?,
        xRequestId: String?,
        xOperatorId: String?,
        xAuthorityId: String?,
    ): ResponseEntity<APIServiceMitigationPostResponse> {
        val action = apIServiceMitigationPostRequest.action?.let(APIPostRequestType::of) ?: APIPostRequestType.UPDATE
        val context =
            createInputContext(
                request = apIServiceMitigationPostRequest,
                ref = serviceMitigationId,
                xADTAuth = xADTAuth,
                xTraceId = xTraceId,
                xRequestId = xRequestId,
                xOperatorId = xOperatorId,
                xAuthorityId = xAuthorityId,
            )
        val errorHandler: (HttpStatus, String?, Exception) -> ResponseEntity<APIServiceMitigationPostResponse> =
            { s, m, _ -> apiResponseEntity(s, context, m) { r -> APIServiceMitigationPostResponse(result = r) } }
        return execute(
            name = "mitigation.update",
            context = context,
            action =
                {
                    if (action == APIPostRequestType.UPDATE) {
                        val modified = mitigationService.updateServiceMitigation(context, serviceMitigationId)
                        apiResponse(HttpStatus.OK, context) { r ->
                            APIServiceMitigationPostResponse(result = r, mitigation = modified)
                        }
                    } else if (action == APIPostRequestType.DELETE) {
                        mitigationService.deleteServiceMitigation(context, serviceMitigationId)
                        apiResponse(HttpStatus.OK, context) { r -> APIServiceMitigationPostResponse(result = r) }
                    } else if (action == APIPostRequestType.APPROVE) {
                        // does not support APPROVE yet
                        apiResponse(HttpStatus.NOT_IMPLEMENTED, context) { r -> APIServiceMitigationPostResponse(result = r) }
                    } else {
                        throw IllegalArgumentException("Invalid action: $action")
                    }
                },
            badRequestHandler = errorHandler,
            internalErrorHandler = errorHandler,
        )
    }
}
