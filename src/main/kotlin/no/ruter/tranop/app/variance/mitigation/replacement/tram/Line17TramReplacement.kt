package no.ruter.tranop.app.variance.mitigation.replacement.tram

import no.ruter.tranop.app.common.api.exception.APIException
import no.ruter.tranop.app.variance.mitigation.replacement.QuayRef
import no.ruter.tranop.app.variance.mitigation.replacement.ReplacementStopCall
import no.ruter.tranop.dated.journey.dto.model.common.DTODatedJourneyDirectionCode
import org.springframework.http.HttpStatus

/**
 * Assumptions:
 * - Tram replacement line starts/ends at Sinsen T instead of Grefsen stasjon (deadrun through Sinsenkrysset).
 * - <PERSON>s plass - Rikshospitalet is serviced similar to 17B today (07-2025).
 * - ReplacementStopCalls at between John Colletts plass and Rikshospitalet may change due to construction site.
 */
class Line17TramReplacement : TramLineReplacement {
    override fun getReplacementLineRef() = "RUT:Line:1702"

    override fun getReplacementStopCalls(directionCode: DTODatedJourneyDirectionCode): Map<QuayRef, ReplacementStopCall?> =
        stopMappings[directionCode]
            ?: throw APIException(HttpStatus.INTERNAL_SERVER_ERROR, "Invalid direction code on journey: $directionCode")

    private val stopMappings =
        mapOf(
            // inbound, direction = 1. Rikshospitalet - Grefsen
            DTODatedJourneyDirectionCode.INBOUND to
                mapOf(
                    // Rikshospitalet (retning Skøyen)
                    "NSR:Quay:11500" to ReplacementStopCall("NSR:Quay:110567", "301232452", "Rikshospitalet"),
                    "NSR:Quay:11501" to ReplacementStopCall("NSR:Quay:110567", "301232452", "Rikshospitalet"),
                    "NSR:Quay:11489" to ReplacementStopCall("NSR:Quay:105730", "301232301", "Gaustadalléen"),
                    "NSR:Quay:11662" to ReplacementStopCall("NSR:Quay:105729", "301037101", "Forskningsparken T"),
                    // Universitetet Blindern (i Problemveien)
                    "NSR:Quay:11651" to ReplacementStopCall("NSR:Quay:109569", "301036601", "Universitetet Blindern"),
                    "NSR:Quay:11625" to ReplacementStopCall("NSR:Quay:105727", "301035005", "John Colletts plass"),
                    "NSR:Quay:11586" to ReplacementStopCall("NSR:Quay:11581", "301034101", "Ullevål sykehus"),
                    "NSR:Quay:11507" to ReplacementStopCall("NSR:Quay:11504", "301031401", "Adamstuen"),
                    "NSR:Quay:11499" to ReplacementStopCall("NSR:Quay:104063", "301031303", "Stensgata"),
                    "NSR:Quay:11492" to ReplacementStopCall("NSR:Quay:104067", "301031203", "Bislett"),
                    "NSR:Quay:11481" to ReplacementStopCall("NSR:Quay:104066", "301030603", "Dalsbergstien"),
                    "NSR:Quay:101335" to ReplacementStopCall("NSR:Quay:101336", "301021104", "Welhavens gate"),
                    "NSR:Quay:7557" to ReplacementStopCall("NSR:Quay:104058", "301005705", "Holbergs plass"),
                    "NSR:Quay:7540" to ReplacementStopCall("NSR:Quay:102839", "301005503", "Tullinløkka"),
                    "NSR:Quay:7529" to ReplacementStopCall("NSR:Quay:101888", "301005303", "Tinghuset"),
                    "NSR:Quay:7497" to ReplacementStopCall("NSR:Quay:106026", "301005003", "Stortorvet"),
                    // Jernbanetorget (Plf E - ved Europarådets plass)
                    "NSR:Quay:7175" to ReplacementStopCall("NSR:Quay:104022", "301000701", "Jernbanetorget"),
                    "NSR:Quay:105273" to ReplacementStopCall("NSR:Quay:105306", "301006403", "Storgata"),
                    "NSR:Quay:104871" to ReplacementStopCall("NSR:Quay:106761", "301051003", "Nybrua"),
                    "NSR:Quay:104055" to ReplacementStopCall("NSR:Quay:11872", "301053105", "Heimdalsgata"),
                    "NSR:Quay:104050" to ReplacementStopCall("NSR:Quay:11878", "301053203", "Lakkegata skole"),
                    "NSR:Quay:104049" to ReplacementStopCall("NSR:Quay:11883", "301053303", "Sofienberg"),
                    "NSR:Quay:11043" to ReplacementStopCall("NSR:Quay:11037", "301140105", "Carl Berners plass"),
                    "NSR:Quay:11053" to ReplacementStopCall("NSR:Quay:104045", "301140503", "Rosenhoff"),
                    // Sinsenterrassen -> Sinsen T
                    "NSR:Quay:11058" to ReplacementStopCall("NSR:Quay:11082", "301210101", "Sinsen T"),
                    // Sinsenkrysset -> utelates?
                    "NSR:Quay:11094" to null,
                    "NSR:Quay:104534" to null,
                    // Grefsen stasjon utelates (?)
                    "NSR:Quay:11105" to null,
                    "NSR:Quay:11104" to null,
                    "NSR:Quay:11103" to null,
                ),
            // outbound, direction = 2. Grefsen - Rikshospitalet
            DTODatedJourneyDirectionCode.OUTBOUND to
                mapOf(
                    // Grefsen stasjon -> utelates?
                    "NSR:Quay:11105" to null,
                    "NSR:Quay:11104" to null,
                    "NSR:Quay:11103" to null,
                    // Sinsenkrysset -> utelates?
                    "NSR:Quay:11095" to null,
                    "NSR:Quay:104534" to null,
                    // Sinsenterrassen -> Sinsen T
                    "NSR:Quay:11057" to ReplacementStopCall("NSR:Quay:11081", "301210102", "Sinsen T"),
                    "NSR:Quay:11054" to ReplacementStopCall("NSR:Quay:104044", "301140504", "Rosenhoff"),
                    "NSR:Quay:11044" to ReplacementStopCall("NSR:Quay:11036", "301140106", "Carl Berners plass"),
                    "NSR:Quay:104048" to ReplacementStopCall("NSR:Quay:11882", "301053304", "Sofienberg"),
                    "NSR:Quay:104051" to ReplacementStopCall("NSR:Quay:11879", "301053204", "Lakkegata skole"),
                    "NSR:Quay:104054" to ReplacementStopCall("NSR:Quay:11873", "301053106", "Heimdalsgata"),
                    "NSR:Quay:104872" to ReplacementStopCall("NSR:Quay:106762", "301051004", "Nybrua"),
                    "NSR:Quay:105274" to ReplacementStopCall("NSR:Quay:105305", "301006404", "Storgata"),
                    // Jernbanetorget (Plf F - ved Europarådets plass)
                    "NSR:Quay:7176" to ReplacementStopCall("NSR:Quay:104023", "301000702", "Jernbanetorget"),
                    "NSR:Quay:100634" to ReplacementStopCall("NSR:Quay:101886", "301005004", "Stortorvet"),
                    "NSR:Quay:7530" to ReplacementStopCall("NSR:Quay:101887", "301005304", "Tinghuset"),
                    "NSR:Quay:7539" to ReplacementStopCall("NSR:Quay:102838", "301005504", "Tullinløkka"),
                    "NSR:Quay:7556" to ReplacementStopCall("NSR:Quay:104059", "301005706", "Holbergs plass"),
                    "NSR:Quay:11484" to ReplacementStopCall("NSR:Quay:104062", "301030704", "Frydenlund"),
                    "NSR:Quay:11480" to ReplacementStopCall("NSR:Quay:104065", "301030604", "Dalsbergstien"),
                    "NSR:Quay:11494" to ReplacementStopCall("NSR:Quay:104068", "301031204", "Bislett"),
                    "NSR:Quay:11498" to ReplacementStopCall("NSR:Quay:104064", "301031304", "Stensgata"),
                    "NSR:Quay:11508" to ReplacementStopCall("NSR:Quay:11502", "301031402", "Adamstuen"),
                    "NSR:Quay:11587" to ReplacementStopCall("NSR:Quay:11583", "301034102", "Ullevål sykehus"),
                    "NSR:Quay:11626" to ReplacementStopCall("NSR:Quay:11620", "301035004", "John Colletts plass"),
                    // Universitetet Blindern (i Problemveien)
                    "NSR:Quay:11650" to ReplacementStopCall("NSR:Quay:109570", "301036602", "Universitetet Blindern"),
                    "NSR:Quay:11663" to ReplacementStopCall("NSR:Quay:105728", "301037102", "Forskningsparken T"),
                    "NSR:Quay:11490" to ReplacementStopCall("NSR:Quay:105731", "301232302", "Gaustadalléen"),
                    // Rikshospitalet (retning Øvre Sogn)
                    "NSR:Quay:11500" to ReplacementStopCall("NSR:Quay:110586", "301232453", "Rikshospitalet"),
                    "NSR:Quay:11501" to ReplacementStopCall("NSR:Quay:110586", "301232453", "Rikshospitalet"),
                ),
        )
}
