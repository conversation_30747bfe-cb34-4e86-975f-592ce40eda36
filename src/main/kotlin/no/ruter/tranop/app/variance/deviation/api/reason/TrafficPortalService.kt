package no.ruter.tranop.app.variance.deviation.api.reason

import no.ruter.tranop.app.common.api.exception.APIException
import no.ruter.tranop.app.common.config.TrafficPortalApiProperties
import okhttp3.OkHttpClient
import okhttp3.Request
import org.springframework.cache.annotation.Cacheable
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.stereotype.Service
import java.util.concurrent.TimeUnit

const val X_API_KEY = "x-api-key"
const val X_USER_ID = "x-user-id"

@Service
class TrafficPortalService(
    val configProperties: TrafficPortalApiProperties,
) {
    private val okHttpClient =
        OkHttpClient
            .Builder()
            .retryOnConnectionFailure(true)
            .readTimeout(configProperties.readTimeoutSeconds, TimeUnit.SECONDS)
            .build()

    /**
     * Gets a list of reason codes from the Traffic Portal API.
     * [operatorRef] is a valid operatorRef from Plandata. E.g. RUT:Operator:130 (Connect bus)
     * @return List of reason codes
     */
    @Cacheable("reasonCode")
    fun getReasonCodes(operatorRef: String): List<ReasonCode> {
        val request =
            Request
                .Builder()
                .url("${configProperties.baseUrl}/v1/reason-codes")
                .addHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .addHeader(X_API_KEY, configProperties.xApiKey)
                .addHeader(X_USER_ID, operatorRef)
                .build()

        okHttpClient.newCall(request).execute().use { response ->
            if (!response.isSuccessful) {
                throw APIException(httpStatus = HttpStatus.valueOf(response.code), msg = response.message, cause = null)
            }
            val responseBody =
                response.body?.string()
                    ?: throw APIException(
                        httpStatus = HttpStatus.NO_CONTENT,
                        msg = "No response from ${configProperties.baseUrl} received",
                    )
            return responseBody.toReasonCodeList()
        }
    }
}

data class ReasonCode(
    val code: String,
    val display: String,
    val displayGroupHeading: String,
    val reasonCategory: String?,
    val reasonSubcategory: String?,
    val requiresDescription: Boolean,
    val applicableEventCategorizations: List<EventCategorization>,
)

data class EventCategorization(
    val category: String,
    val subcategory: String,
    val dimensions: List<String>,
)

enum class ReasonCodeCategory {
    CANCELLATION,
    DELAY,
    OTHER_EVENT,
    ;

    companion object {
        fun valueOfOrNull(value: String): ReasonCodeCategory? = runCatching { ReasonCodeCategory.valueOf(value) }.getOrNull()
    }
}

enum class ReasonCodeSubCategory {
    REALTIME_PROBLEM,
    INCOMPLETE_SERVICE,
    ;

    companion object {
        fun valueOfOrNull(value: String): ReasonCodeSubCategory? = runCatching { ReasonCodeSubCategory.valueOf(value) }.getOrNull()
    }
}
