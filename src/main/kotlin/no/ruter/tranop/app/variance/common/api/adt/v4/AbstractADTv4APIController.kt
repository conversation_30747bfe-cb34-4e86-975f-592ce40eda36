package no.ruter.tranop.app.variance.common.api.adt.v4

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.api.AbstractAPIController
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MappingDetails
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.variance.common.api.adt.ADTAuthTrace
import no.ruter.tranop.app.variance.common.api.adt.v4.input.ADTv4InputContext
import no.ruter.tranop.assignment.adt.v4.model.APIStatusResponse
import no.ruter.tranop.assignment.adt.v4.model.APIStatusResponseDetail
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseCode
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseReasonCode
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity

abstract class AbstractADTv4APIController(
    channel: DataChannel,
    protected val timeService: TimeService,
    insightService: InsightService,
) : AbstractAPIController(
        channel = channel,
        insightService = insightService,
    ) {
    companion object {
        private val VALID_REASONS = APIStatusResponseReasonCode.ALL.map(APIStatusResponseReasonCode::value).toSet()
    }

    protected fun <R> createInputContext(
        request: R,
        ref: String?,
        xADTAuth: String?,
        xTraceId: String?,
        xRequestId: String?,
        xOperatorId: String?,
        xAuthorityId: String?,
    ): ADTv4InputContext<R> {
        val trace =
            ADTAuthTrace(
                xADTAuth = xADTAuth,
                xTraceId = xTraceId,
                xRequestId = xRequestId,
                xOperatorId = xOperatorId,
                xAuthorityId = xAuthorityId,
            )
        return ADTv4InputContext(
            now = timeService.now(),
            request = request,
            requestTrace = trace,
            channel = channel,
        )
    }

    protected fun <T, O, C : ADTv4InputContext<O>> apiResponse(
        status: HttpStatus = HttpStatus.OK,
        context: C,
        message: String? = null,
        headers: HttpHeaders = HttpHeaders.EMPTY,
        creator: (APIStatusResponse) -> T,
    ): Triple<T, HttpStatus, HttpHeaders> {
        var reason: String? = null
        val code: APIStatusResponseCode =
            if (status.is2xxSuccessful || status.is1xxInformational) {
                APIStatusResponseCode.OK
            } else if (status.is4xxClientError) {
                APIStatusResponseCode.ERR_CLIENT
            } else {
                if (status == HttpStatus.BAD_REQUEST) {
                    reason = APIStatusResponseReasonCode.BAD_REQUEST.value
                }
                APIStatusResponseCode.ERR_SERVER
            }
        val st =
            APIStatusResponseDetail(
                code = code.value,
                reason = reason,
                description = message ?: status.reasonPhrase,
            )
        val details = mapDetails(context)
        val result = APIStatusResponse(status = st, details = details.ifEmpty { null })
        return Triple(creator(result), status, headers)
    }

    private fun <O, C : ADTv4InputContext<O>> mapDetails(context: C): List<APIStatusResponseDetail> {
        val res = ArrayList<APIStatusResponseDetail>()
        if (context.hasErrors) {
            context.errors.forEach { error ->
                res.add(mapInputContextStatusDetail(input = error, fallbackReason = APIStatusResponseReasonCode.BAD_REQUEST))
            }
            context.warnings.forEach { warning ->
                res.add(mapInputContextStatusDetail(input = warning, fallbackReason = APIStatusResponseReasonCode.BAD_REQUEST))
            }
            context.info.forEach { info ->
                res.add(mapInputContextStatusDetail(input = info, fallbackReason = APIStatusResponseReasonCode.INFO))
            }
        }
        return res
    }

    private fun mapInputContextStatusDetail(
        input: MappingDetails,
        fallbackReason: APIStatusResponseReasonCode,
    ): APIStatusResponseDetail {
        val msg = input.msg
        val actualReason = input.reason
        val publicReason = if (VALID_REASONS.contains(actualReason)) actualReason else fallbackReason.value
        return APIStatusResponseDetail(
            code = null,
            reason = publicReason,
            description = msg,
        )
    }

    protected fun <T, O, C : ADTv4InputContext<O>> apiResponseEntity(
        status: HttpStatus = HttpStatus.OK,
        context: C,
        message: String?,
        creator: (APIStatusResponse) -> T,
    ): ResponseEntity<T> {
        val (res, st, headers) = apiResponse(status, context, message, HttpHeaders.EMPTY, creator)
        return ResponseEntity.status(st).headers(headers).body(res)
    }
}
