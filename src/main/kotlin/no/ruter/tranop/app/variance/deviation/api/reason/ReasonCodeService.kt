package no.ruter.tranop.app.variance.deviation.api.reason

import no.ruter.tranop.app.common.TraceInfoContext
import no.ruter.tranop.assignment.adt.v4.model.APIServiceDeviationReasonCodeListResponse
import no.ruter.tranop.assignment.adt.v4.model.APIStatusResponse
import no.ruter.tranop.assignment.adt.v4.model.APIStatusResponseDetail
import no.ruter.tranop.assignment.adt.v4.model.value.APIServiceDeviationCode
import no.ruter.tranop.assignment.adt.v4.model.value.APIStatusResponseCode
import org.springframework.stereotype.Service

@Service
class ReasonCodeService(
    val trafficPortalService: TrafficPortalService,
) {
    fun getReasonCodes(
        context: TraceInfoContext,
        serviceDeviationCode: APIServiceDeviationCode?,
    ): APIServiceDeviationReasonCodeListResponse {
        val reasonCodes = context.trace.operatorId?.let(trafficPortalService::getReasonCodes) ?: emptyList()
        return APIServiceDeviationReasonCodeListResponse(
            result =
                APIStatusResponse(
                    status =
                        APIStatusResponseDetail(
                            code = APIStatusResponseCode.OK.value,
                            description = APIStatusResponseCode.OK.description,
                        ),
                ),
            groups = reasonCodes.distinctBy { it.toGroupCode() }.map { it.toDeviationReasonGroup() },
            reasons =
                reasonCodes
                    .map { it.toDeviationReasonCode() }
                    .filter {
                        serviceDeviationCode == null || it.validDeviationCodes?.contains(serviceDeviationCode) == true
                    },
        )
    }
}
