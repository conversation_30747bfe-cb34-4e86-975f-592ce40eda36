package no.ruter.tranop.app.variance.deviation.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.base.BaseRecordQueryBuilder
import no.ruter.tranop.assignmentmanager.db.sql.tables.DeviationTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DeviationRecord

class ServiceDeviationQueryBuilder :
    BaseRecordQueryBuilder<
        DeviationRecord,
        DeviationTable,
        ServiceDeviationQueryBuilder,
    >(
        recordType = TYPE,
    ) {
    companion object {
        val TYPE = RecordType.SERVICE_DEVIATION
    }
}
