package no.ruter.tranop.app.variance.mitigation

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.variance.common.ServiceImpactContext
import no.ruter.tranop.app.variance.common.ServiceVarianceRequest
import no.ruter.tranop.app.variance.mitigation.db.InternalServiceMitigation

class ServiceMitigationRequest(
    type: Type,
    trace: TraceInfo,
    impact: ServiceImpactContext,
    channel: DataChannel,
    val mitigation: InternalServiceMitigation,
) : ServiceVarianceRequest(type, trace, impact, channel)
