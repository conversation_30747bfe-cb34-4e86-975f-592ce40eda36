package no.ruter.tranop.app.variance.common

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.mapping.MappingDetails
import no.ruter.tranop.app.common.time.DateTimeRange
import no.ruter.tranop.app.variance.common.spec.JourneyCallSpec
import no.ruter.tranop.app.variance.common.spec.JourneyId
import no.ruter.tranop.app.variance.common.spec.JourneySpec
import no.ruter.tranop.app.variance.common.spec.window.JourneyCallSpecWindow
import no.ruter.tranop.app.variance.common.spec.window.JourneyLineSpecWindow
import no.ruter.tranop.app.variance.common.spec.window.JourneySpecWindow
import no.ruter.tranop.app.variance.common.spec.window.JourneySpecWindowOption
import no.ruter.tranop.app.variance.common.spec.window.StopPointSpecWindow
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.common.dto.model.DTODateTimeRange
import no.ruter.tranop.operation.common.dto.model.DTOServiceImpact
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneyCallSpec
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneyLineSpec
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneyLineSpecWindow
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneySpec
import no.ruter.tranop.operation.common.dto.model.spec.DTOJourneySpecWindowOption
import no.ruter.tranop.operation.common.dto.model.spec.DTOStopPointSpec
import no.ruter.tranop.operation.common.dto.model.spec.DTOStopPointSpecWindow
import no.ruter.tranop.operation.common.dto.value.DTOJourneyDirectionCode
import java.time.OffsetDateTime

class ServiceImpactValidator {
    private val rootPath = "$.spec" // We assume all impacts have same rot path, for now (true for deviation and mitigation in ADT v4).

    fun validateImpact(
        type: RecordType<*, *>,
        impact: DTOServiceImpact?,
        duration: DTODateTimeRange?,
    ): ServiceImpactContext {
        val res = ServiceImpactContext(source = impact)
        val errors = res.errors

        // Validate duration, if set.
        duration?.let { d ->
            mapWindow(d, path = "$rootPath.duration", errors = errors, desc = "duration")?.let {
                res.duration = it
            }
        }

        // Only continue if impact is actually set.
        if (impact == null) {
            errors.add(MappingDetails.error(rootPath, "missing required ${type.desc} impact"))
            return res
        }

        // Validate lines.
        val impactPath = "$rootPath.impact"
        val outputLines = res.lines
        impact.lines?.forEachIndexed { i, line ->
            validateLineSpecWindow(line, path = "$impactPath.lines[$i]", errors = res.lineErrors)?.let {
                outputLines.add(it)
            }
        }

        // Validate journeys
        val outputJourneys = res.journeys
        impact.journeys.forEachIndexed { i, journey ->
            validateJourneySpecWindowOption(journey, path = "$impactPath.journeys[$i]", context = res)?.let {
                outputJourneys.add(it)
            }
        }

        // Validate stop points
        val outputStopPoints = res.stopPoints
        impact.stopPoints?.forEachIndexed { i, stop ->
            validateStopPointSpecWindow(stop, path = "$impactPath.stopPoints[$i]", errors = res.stopPointErrors)?.let {
                outputStopPoints.add(it)
            }
        }

        // Ensure impact is non-empty.
        if (outputLines.isEmpty() && outputJourneys.isEmpty() && outputStopPoints.isEmpty()) {
            val msg = "missing required line(s), journey(s) or stop points(s) in ${type.desc} impact"
            errors.add(MappingDetails.error(impactPath, msg))
        }
        return res
    }

    private fun validateLineSpec(
        spec: DTOJourneyLineSpec?,
        path: String,
        errors: ArrayList<MappingDetails>,
    ): DTOJourneyLineSpec? {
        if (spec == null) {
            errors.add(MappingDetails.error(path, "missing line spec"))
            return null
        }

        val lineId = trim(spec.lineId)
        if (lineId.isNullOrEmpty()) {
            errors.add(MappingDetails.error(path, "missing line id"))
            return null
        }
        val direction = spec.direction
        if (direction != null && !DTOJourneyDirectionCode.ALL.contains(direction)) {
            errors.add(MappingDetails.error(path, "invalid line direction: ${direction.value}"))
            return null
        }

        return DTOJourneyLineSpec().apply {
            this.lineId = lineId
            this.direction = direction
        }
    }

    private fun validateLineSpecWindow(
        line: DTOJourneyLineSpecWindow?,
        path: String,
        errors: ArrayList<MappingDetails>,
    ): JourneyLineSpecWindow? {
        if (line == null) return null

        val lineSpec = validateLineSpec(line.spec, path = "$path.spec", errors = errors)
        val lineWindow = mapWindow(line.serviceWindow, path = "$path.serviceWindow", errors = errors)
        return JourneyLineSpecWindow(spec = lineSpec, source = line, window = lineWindow)
    }

    private fun validateStopPointSpec(
        spec: DTOStopPointSpec?,
        path: String,
        errors: MutableList<MappingDetails>,
    ): DTOStopPointSpec? {
        if (spec == null) return null

        val quayId = trim(spec.quayId)
        val stopPointId = trim(spec.stopPointId)
        return if (quayId == null && stopPointId == null) {
            errors.add(MappingDetails.error(path, msg = "invalid stop point spec: missing quay id or stop point id"))
            null
        } else {
            DTOStopPointSpec().apply {
                this.quayId = quayId
                this.stopPointId = stopPointId
            }
        }
    }

    private fun validateStopPointSpecWindow(
        stop: DTOStopPointSpecWindow?,
        path: String,
        errors: ArrayList<MappingDetails>,
    ): StopPointSpecWindow? {
        if (stop == null) return null

        val spec = validateStopPointSpec(stop.spec, path = "$path.spec", errors = errors)
        val window = mapWindow(stop.serviceWindow, path = "$path.serviceWindow", errors = errors)
        return StopPointSpecWindow(spec = spec, source = stop, window = window)
    }

    private fun validateJourneySpec(
        spec: DTOJourneySpec?,
        path: String,
        errors: ArrayList<MappingDetails>,
    ): JourneySpec? {
        if (spec == null) {
            errors.add(MappingDetails.error(path, "missing journey spec"))
            return null
        }

        val lineId = spec.lineId?.trim()
        val journeyId = JourneyId.parse(spec.journeyId)
        val departureTime =
            spec.firstDepartureDateTime?.let { departure ->
                mapDateTime(departure, path = "$path.firstDepartureDateTime", desc = "departure", errors = errors)
            }

        // TODO: Figure out what the actual rules / requirements are supposed to be here...
        val errorCount = errors.size
        if (journeyId == null) {
            errors.add(MappingDetails.error(path, msg = "missing journey id"))
            return null
        }
        if (journeyId.lineRequired && lineId == null) {
            errors.add(MappingDetails.error(path, msg = "missing valid journey line"))
        }
        if (journeyId.departureRequired && departureTime == null) {
            errors.add(MappingDetails.error(path, msg = "missing valid journey departure time"))
        }
        if (errors.size > errorCount) {
            return null // Allow both departure time and line id errors to be recorded before returning null to caller.
        }

        return JourneySpec(
            lineId = lineId,
            journeyId = journeyId,
            departureTime = departureTime,
        )
    }

    private fun validateJourneyCallSpec(
        spec: DTOJourneyCallSpec?,
        path: String,
        errors: ArrayList<MappingDetails>,
    ): JourneyCallSpecWindow? {
        if (spec == null) return null

        val stopPointPath = "$path.stopPoint"
        val stopPoint =
            spec.stopPoint?.let {
                validateStopPointSpec(it, path = stopPointPath, errors = errors)
            }
        if (stopPoint == null) {
            errors.add(MappingDetails.error(path = stopPointPath, msg = "$stopPointPath: missing required stop point spec"))
        }
        val callArrival =
            spec.arrivalDateTime?.let { arrival ->
                mapDateTime(
                    value = arrival,
                    path = "$path.arrivalDateTime",
                    desc = "arrival",
                    errors = errors,
                )
            }
        val callDeparture =
            spec.departureDateTime?.let { departure ->
                mapDateTime(
                    value = departure,
                    path = "$path.departureDateTime",
                    desc = "departure",
                    errors = errors,
                )
            }
        if (callArrival == null && callDeparture == null) {
            errors.add(MappingDetails.error(path, msg = "missing required arrival or departure time"))
        }
        val output = JourneyCallSpec(stopPoint, arrivalTime = callArrival, departureTime = callDeparture)
        return JourneyCallSpecWindow(output, source = spec)
    }

    private fun validateJourneySpecWindowOption(
        option: DTOJourneySpecWindowOption?,
        path: String,
        context: ServiceImpactContext,
    ): JourneySpecWindowOption? {
        if (option == null) return null

        val output = JourneySpecWindowOption()
        val outputCalls = output.calls
        option.calls?.forEachIndexed { j, call ->
            validateJourneyCallSpec(call, path = "$path.calls[$j]", errors = context.callErrors)?.let {
                outputCalls.add(it)
            }
        }

        val journeyErrors = context.journeyErrors
        option.journey?.let {
            val journeyPath = "$path.journey"
            val journeySpec = validateJourneySpec(it.spec, path = "$journeyPath.spec", errors = journeyErrors)
            val journeyWindow = mapWindow(it.serviceWindow, path = "$journeyPath.serviceWindow", errors = journeyErrors)
            val journeySpecWindow = JourneySpecWindow(spec = journeySpec, source = it, window = journeyWindow)
            output.journey = journeySpecWindow
        }
        if (output.isEmpty()) {
            journeyErrors.add(MappingDetails.error(path, msg = "missing journey or call list"))
            return null
        }

        return output
    }

    private fun mapWindow(
        range: DTODateTimeRange?,
        path: String,
        errors: MutableList<MappingDetails>,
        desc: String = "service window",
    ): DateTimeRange? {
        if (range == null) return null
        val end = mapDateTime(range.end, path = "$path.end", desc = "$desc end", errors)
        val start = mapDateTime(range.start, path = "$path.start", desc = "$desc start", errors)
        return if (end == null && start == null) {
            null
        } else if (start?.compareTo(end) == 0) {
            errors.add(MappingDetails.error(path, msg = "$desc is zero: end time [$end] is same as start time [$start]"))
            null
        } else if (start?.isBefore(end) == false) {
            errors.add(MappingDetails.error(path, msg = "$desc is negative: end time [$end] is before start time [$start]"))
            null
        } else {
            DateTimeRange(start = start, end = end)
        }
    }

    private fun mapDateTime(
        value: String?,
        path: String,
        desc: String,
        errors: MutableList<MappingDetails>,
    ): OffsetDateTime? {
        val res = value.toOffsetDateTime()
        if (res == null) {
            errors.add(MappingDetails.error(path, msg = "invalid $desc timestamp: $value"))
        }
        return res
    }

    private fun trim(value: String?): String? = value?.trim()?.ifEmpty { null }
}
