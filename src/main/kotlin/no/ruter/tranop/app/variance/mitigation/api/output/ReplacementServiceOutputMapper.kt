package no.ruter.tranop.app.variance.mitigation.api.output

import no.ruter.tranop.app.plan.journey.JourneyUtils
import no.ruter.tranop.app.variance.mitigation.JourneyReplacements
import no.ruter.tranop.assignment.adt.v4.model.APIDateTimeRange
import no.ruter.tranop.assignment.adt.v4.model.APIJourney
import no.ruter.tranop.assignment.adt.v4.model.APIJourneyIdentifiers
import no.ruter.tranop.assignment.adt.v4.model.APIJourneySpec
import no.ruter.tranop.assignment.adt.v4.model.APIServiceReplacement
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney

class ReplacementServiceOutputMapper {
    fun mapReplacement(input: JourneyReplacements): APIServiceReplacement =
        APIServiceReplacement(
            replaced = input.replaced.map(::mapJourney),
            replacements = input.replacements.map(::mapJourney),
        )

    private fun mapJourney(input: DTODatedJourney): APIJourney {
        val lastArrival = (input.plan?.lastArrivalDateTime ?: JourneyUtils.resolveArrivals(input).lastOrNull())?.toString()
        val firstDeparture = (input.plan?.firstDepartureDateTime ?: JourneyUtils.resolveDepartures(input).firstOrNull())?.toString()

        val refs = input.journeyReferences
        val spec =
            APIJourneySpec(
                lineId = input.line?.lineRef,
                journeyId = refs?.datedServiceJourneyId,
                firstDepartureDateTime = firstDeparture,
            )
        val window = APIDateTimeRange(end = lastArrival, start = firstDeparture)
        val journeyIds =
            APIJourneyIdentifiers(
                vehicleJourneyId = refs?.vehicleJourneyId,
                serviceJourneyId = refs?.externalJourneyRef, // TODO: Is this mapping correct?
                datedServiceJourneyId = refs?.datedServiceJourneyId,
            )
        return APIJourney(
            spec = spec,
            journeyIds = journeyIds,
            serviceWindow = window,
        )
    }
}
