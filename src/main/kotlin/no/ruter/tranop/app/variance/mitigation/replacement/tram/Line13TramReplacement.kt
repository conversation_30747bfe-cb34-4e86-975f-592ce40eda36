package no.ruter.tranop.app.variance.mitigation.replacement.tram

import no.ruter.tranop.app.common.api.exception.APIException
import no.ruter.tranop.app.variance.mitigation.replacement.QuayRef
import no.ruter.tranop.app.variance.mitigation.replacement.ReplacementStopCall
import no.ruter.tranop.dated.journey.dto.model.common.DTODatedJourneyDirectionCode
import org.springframework.http.HttpStatus

class Line13TramReplacement : TramLineReplacement {
    override fun getReplacementLineRef() = "RUT:Line:1302"

    override fun getReplacementStopCalls(directionCode: DTODatedJourneyDirectionCode): Map<QuayRef, ReplacementStopCall?> =
        stopMappings[directionCode]
            ?: throw APIException(HttpStatus.INTERNAL_SERVER_ERROR, "Invalid direction code on journey: $directionCode")

    private val stopMappings =
        mapOf(
            // inbound, direction = 1
            DTODatedJourneyDirectionCode.INBOUND to
                mapOf(
                    "NSR:Quay:11899" to ReplacementStopCall("NSR:Quay:11905", "301254101", "Lilleaker"),
                    // Sollerud betjenes ikke
                    "NSR:Quay:11894" to null,
                    "NSR:Quay:11885" to ReplacementStopCall("NSR:Quay:104533", "301253021", "Furulund"),
                    // Ullern betjenes ikke
                    "NSR:Quay:11874" to null,
                    // Abbediengen betjenes ikke
                    "NSR:Quay:11870" to null,
                    // Hoff betjenes ikke
                    "NSR:Quay:11870" to null,
                    // Skøyen stasjon plattform E
                    "NSR:Quay:11833" to ReplacementStopCall("NSR:Quay:11819", "301250121", "Skøyen"),
                    "NSR:Quay:11840" to ReplacementStopCall("NSR:Quay:11845", "301250713", "Thune"),
                    "NSR:Quay:7781" to ReplacementStopCall("NSR:Quay:101398", "3010123", "Nobes gate"),
                    "NSR:Quay:7770" to ReplacementStopCall("NSR:Quay:102104", "3010122", "Skarpsno"),
                    "NSR:Quay:7765" to ReplacementStopCall("NSR:Quay:102102", "301012003", "Skillebekk"),
                    "NSR:Quay:7731" to ReplacementStopCall("NSR:Quay:104031", "301011003", "Solli"),
                    // Nationaltheatret plattform D
                    "NSR:Quay:7358" to ReplacementStopCall("NSR:Quay:7349", "301003201", "Nationaltheatret"),
                    "NSR:Quay:7279" to ReplacementStopCall("NSR:Quay:105725", "301002103", "Øvre Slottsgate"),
                    "NSR:Quay:7246" to ReplacementStopCall("NSR:Quay:105723", "301001905", "Dronningens gate"),
                    "NSR:Quay:7167" to ReplacementStopCall("NSR:Quay:7169", "301000601", "Bjørvika"),
                    "NSR:Quay:104526" to ReplacementStopCall("NSR:Quay:104410", "301000401", "Middelalderparken"),
                    "NSR:Quay:12065" to ReplacementStopCall("NSR:Quay:106028", "301063003", "Oslo Hospital"),
                    "NSR:Quay:12139" to ReplacementStopCall("NSR:Quay:12145", "301071101", "Ekebergparken"),
                    "NSR:Quay:12190" to ReplacementStopCall("NSR:Quay:12195", "301073101", "Sportsplassen"),
                    "NSR:Quay:12219" to ReplacementStopCall("NSR:Quay:12215", "301073901", "Holtet"),
                    "NSR:Quay:12316" to ReplacementStopCall("NSR:Quay:12321", "301083101", "Sørli"),
                    "NSR:Quay:12322" to ReplacementStopCall("NSR:Quay:12325", "301084101", "Kastellet"),
                    "NSR:Quay:12332" to ReplacementStopCall("NSR:Quay:12339", "301085201", "Bråten"),
                    "NSR:Quay:12348" to ReplacementStopCall("NSR:Quay:12366", "301086721", "Sæter"),
                    "NSR:Quay:12369" to ReplacementStopCall("NSR:Quay:12371", "301087101", "Ljabru"),
                ),
            // outbound, direction = 2
            DTODatedJourneyDirectionCode.OUTBOUND to
                // outbound, direction = 2
                mapOf(
                    "NSR:Quay:12368" to ReplacementStopCall("NSR:Quay:12370", "301087102", "Ljabru"),
                    "NSR:Quay:12349" to ReplacementStopCall("NSR:Quay:12367", "301086722", "Sæter"),
                    "NSR:Quay:12333" to ReplacementStopCall("NSR:Quay:12338", "301085202", "Bråten"),
                    "NSR:Quay:12323" to ReplacementStopCall("NSR:Quay:12324", "301084102", "Kastellet"),
                    "NSR:Quay:12317" to ReplacementStopCall("NSR:Quay:12320", "301083102", "Sørli"),
                    "NSR:Quay:12220" to ReplacementStopCall("NSR:Quay:12216", "301073902", "Holtet"),
                    "NSR:Quay:12191" to ReplacementStopCall("NSR:Quay:12193", "301073102", "Sportsplassen"),
                    // Jomfrubråten betjenes ikke
                    "NSR:Quay:12152" to null,
                    "NSR:Quay:12138" to ReplacementStopCall("NSR:Quay:12144", "301071102", "Ekebergparken"),
                    "NSR:Quay:12066" to ReplacementStopCall("NSR:Quay:106029", "301063004", "Oslo Hospital"),
                    "NSR:Quay:104525" to ReplacementStopCall("NSR:Quay:104409", "301000402", "Middelalderparken"),
                    "NSR:Quay:7168" to ReplacementStopCall("NSR:Quay:7170", "301000602", "Bjørvika"),
                    "NSR:Quay:7245" to ReplacementStopCall("NSR:Quay:105722", "301001906", "Dronningens gate"),
                    "NSR:Quay:7277" to ReplacementStopCall("NSR:Quay:105724", "301002104", "Øvre Slottsgate"),
                    // Nationaltheatret plattform A
                    "NSR:Quay:7357" to ReplacementStopCall("NSR:Quay:7350", "301003202", "Nationaltheatret"),
                    "NSR:Quay:7732" to ReplacementStopCall("NSR:Quay:104030", "301011004", "Solli"),
                    "NSR:Quay:7764" to ReplacementStopCall("NSR:Quay:102101", "301012004", "Skillebekk"),
                    "NSR:Quay:7771" to ReplacementStopCall("NSR:Quay:102103", "301012204", "Skarpsno"),
                    "NSR:Quay:7780" to ReplacementStopCall("NSR:Quay:101397", "301012304", "Nobels gate"),
                    "NSR:Quay:11841" to ReplacementStopCall("NSR:Quay:11844", "301250714", "Thune"),
                    "NSR:Quay:11834" to ReplacementStopCall("NSR:Quay:11817", "301250104", "Skøyen stasjon"),
                    // Hoff betjenes ikke
                    "NSR:Quay:11851" to null,
                    // Abbediengen betjenes ikke
                    "NSR:Quay:11871" to null,
                    // Ullern betjenes ikke
                    "NSR:Quay:11875" to null,
                    "NSR:Quay:11884" to ReplacementStopCall("NSR:Quay:104533", "301253021", "Furulund"),
                    // Sollerud betjenes ikke
                    "NSR:Quay:11896" to null,
                    "NSR:Quay:11901" to ReplacementStopCall("NSR:Quay:11907", "301254102", "Lilleaker"),
                ),
        )
}
