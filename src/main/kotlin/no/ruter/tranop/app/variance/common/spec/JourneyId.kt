package no.ruter.tranop.app.variance.common.spec

/** A journey identifier with a type, a (parsed) value and a raw (encoded) value. **/
class JourneyId(
    val type: Type,
    val value: String,
    val rawValue: String = value,
) {
    private val strValue = "${type.name}($rawValue)"

    val lineRequired = type.lineRequired
    val departureRequired = type.departureRequired

    override fun equals(other: Any?): Boolean = if (other is JourneyId) this.strValue == other.strValue else false

    override fun hashCode(): Int = strValue.hashCode()

    override fun toString(): String = strValue

    data class Encoding(
        val sep: String = "",
        val prefix: String = "",
        val prefixIndex: Int = 0,
    )

    enum class Type(
        val dated: Boolean = false,
        val encodings: List<Encoding>,
        val lineRequired: Boolean = false,
    ) {
        UNKNOWN(
            encodings = emptyList(),
        ),
        DATED_JOURNEY_ID(
            dated = true,
            encodings =
                listOf(
                    Encoding(sep = "", prefix = "djj-", prefixIndex = 0),
                    Encoding(sep = "", prefix = "dj-", prefixIndex = 0),
                    Encoding(sep = "", prefix = "jr", prefixIndex = 0),
                ),
        ),
        VEHICLE_JOURNEY_ID(
            encodings = emptyList(),
            lineRequired = true,
        ),
        SERVICE_JOURNEY_ID(
            encodings =
                listOf(
                    Encoding(sep = ":", "ServiceJourney", prefixIndex = 1),
                    Encoding(sep = ":", "ServiceJourneyId", prefixIndex = 1),
                ),
        ),
        DATED_SERVICE_JOURNEY_ID(
            dated = true,
            encodings =
                listOf(
                    Encoding(sep = ":", "DatedServiceJourney", prefixIndex = 1),
                    Encoding(sep = ":", "DatedServiceJourneyId", prefixIndex = 1),
                ),
        ),
        ;

        val departureRequired: Boolean = !dated
    }

    companion object {
        // Group encodings with separators in a map keyed by separator.
        private val SEP_TYPES =
            LinkedHashMap<String, ArrayList<Pair<Type, Encoding>>>().apply {
                Type.entries.forEach { type ->
                    type.encodings.forEach { encoding ->
                        val sep = encoding.sep
                        if (sep.isNotEmpty()) {
                            computeIfAbsent(sep) { ArrayList<Pair<Type, Encoding>>() }.add(type to encoding)
                        }
                    }
                }
            }

        // "Group" encodings with simple prefixes in a list of encodings and their associated journey id type.
        val PREFIX_TYPES =
            ArrayList<Pair<Type, Encoding>>().apply {
                Type.entries.forEach { type ->
                    type.encodings.forEach { encoding ->
                        if (encoding.sep.isEmpty()) {
                            if (encoding.prefix.isNotEmpty()) {
                                add(type to encoding)
                            }
                        }
                    }
                }
            }

        fun parse(value: String?): JourneyId? {
            val v = value?.trim()?.ifEmpty { null } ?: return null

            // Parse types with a simple prefix
            for ((type, encoding) in PREFIX_TYPES) {
                if (v.startsWith(encoding.prefix)) {
                    return JourneyId(type, value = v)
                }
            }

            // Parse types with a segmented prefix
            for ((sep, types) in SEP_TYPES.entries) {
                if (v.contains(sep)) {
                    val segments = v.split(sep)
                    val numSegments = segments.size
                    for ((type, encoding) in types) {
                        val i = encoding.prefixIndex
                        if (numSegments > i) { // larger, not equal, since prefix requires at least one segment _after_ it.
                            if (segments[i] == encoding.prefix) {
                                val stripped = segments.takeLast(numSegments - (i + 1)).joinToString(sep)
                                return JourneyId(type = type, value = stripped, rawValue = v)
                            }
                        }
                    }
                }
            }

            return JourneyId(type = Type.VEHICLE_JOURNEY_ID, value = v)
        }
    }
}
