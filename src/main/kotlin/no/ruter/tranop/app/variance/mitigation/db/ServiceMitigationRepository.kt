package no.ruter.tranop.app.variance.mitigation.db

import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.db.record.base.BaseRecordRepository
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.assignmentmanager.db.sql.tables.MitigationTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.MitigationRecord
import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigation
import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigationSpec
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class ServiceMitigationRepository(
    dslContext: DSLContext,
    timeService: TimeService,
    insightService: InsightService,
) : BaseRecordRepository<
        MitigationRecord,
        MitigationTable,
        InternalServiceMitigation,
        ServiceMitigationRecordMapper,
    >(
        recordType = TYPE,
        dslContext = dslContext,
        sortFields = ORDER_FIELDS,
        timeService = timeService,
        insightService = insightService,
        recordMapper = ServiceMitigationRecordMapper(insightService),
    ) {
    companion object {
        val TYPE = ServiceMitigationQueryBuilder.TYPE
        val TABLE = TYPE.table

        val ORDER_FIELDS =
            listOf(
                TABLE.CREATED_AT.asc(),
                TABLE.MODIFIED_AT.asc(),
                TABLE.ID.asc(),
            )
    }

    fun queryBuilder() = ServiceMitigationQueryBuilder()

    fun newRecord(spec: DTOServiceMitigationSpec): InternalServiceMitigation {
        val ref = MapperUtils.randomId(prefix = "sm-")
        val record = dslContext.newRecord(table)
        return wrapSpec(ref, spec, record)
    }

    fun wrapSpec(
        ref: String,
        spec: DTOServiceMitigationSpec,
        record: MitigationRecord,
//        draft: Boolean
    ): InternalServiceMitigation {
        val wrapper =
            DTOServiceMitigation().apply {
                this.ref = ref
                this.spec = spec
//                this.draft = draft
            }
        return InternalServiceMitigation(wrapper, record)
    }

    fun storeRecord(
        data: InternalServiceMitigation,
        trace: TraceInfo,
        now: OffsetDateTime? = null,
    ): Boolean {
        val t = now ?: timeService.now()
        val record = data.record
        recordMapper.updateRecord(source = data.data, record = record, trace = trace, now = t)
        return record.store() == 1
    }

    fun fetchByRef(
        ref: String?,
        trace: TraceInfo? = null,
        restricted: Boolean = true,
        includeDeleted: Boolean = !restricted,
    ): InternalServiceMitigation? {
        val b = queryBuilder().ref(ref).authorized(trace, restricted, includeDeleted)
        return fetch(b).firstOrNull()
    }
}
