package no.ruter.tranop.app.variance.mitigation.replacement

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.TraceInfo
import no.ruter.tranop.app.common.api.exception.APIException
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.stop.db.InternalStopPoint
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.app.plan.stop.input.StopPointInputContext
import no.ruter.tranop.assignment.util.toIsoString
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import org.springframework.http.HttpStatus

/**
 * Stop points table may not contain all stop points in replacement journey when returned for the first time,
 * so this class creates a new stop point DTO if it doesn't already exist.
 */
class ReplacementStopPointService(
    private val stopPointRepo: StopPointRepository,
    private val timeService: TimeService,
    infoProperties: AppInfoProperties,
) {
    private val appName = infoProperties.name

    fun findOrCreateStopPoint(
        quayRef: String,
        legacyQuayRef: String,
        name: String,
        traceInfo: TraceInfo,
    ): InternalStopPoint {
        val now = timeService.now().toIsoString()
        val stored = stopPointRepo.fetchByQuayId(quayRef).firstOrNull()

        if (stored != null) {
            return stored
        }

        val datedJourneyStopPointRef = MapperUtils.toDatedJourneyStopPointRef(quayRef, legacyQuayRef)
        val dtoStopPoint =
            DTOStopPoint().apply {
                this.ref = datedJourneyStopPointRef
                this.quayRef = quayRef
                this.legacyQuayRef = legacyQuayRef
                this.name = name
                this.events =
                    listOf(
                        DTOEvent().apply {
                            this.type = DTOEventType.CREATED
                            this.source = appName
                            this.traceId = traceInfo.traceId
                            this.timestamp = now
                            this.description = "The stop-point was created by assignment-journey-manager."
                            this.metadata = emptyList()
                        },
                    )
            }

        val stopPointInputContext =
            StopPointInputContext(
                stopPoint = dtoStopPoint,
                channel = RecordType.SERVICE_MITIGATION.channels.input,
            )
        val store = stopPointRepo.store(stopPointInputContext)
        if (!store) {
            throw APIException(
                httpStatus = HttpStatus.BAD_REQUEST,
                msg = "Unable to create stop point",
            )
        }

        return stopPointRepo.fetchByRef(datedJourneyStopPointRef) ?: throw APIException(
            httpStatus = HttpStatus.BAD_REQUEST,
            msg = "Unable to find newly created stop point",
        )
    }
}
