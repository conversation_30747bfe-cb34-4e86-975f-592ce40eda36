package no.ruter.tranop.app.plan.link.output.entity

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.mapping.input.InputMessage
import no.ruter.tranop.app.common.mapping.output.AbstractOutputContext
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLink
import no.ruter.tranop.journey.dated.dto.metadata

class StopPointLinkOutputEntityContext(
    input: InputMessage<DTOStopPointLink, StopPointLinkInputContext>,
) : AbstractOutputContext<DTOStopPointLink, StopPointLinkInputContext>(input) {
    override val traceId: String?
        get() = input.context.traceId
    override val channel: DataChannel
        get() = input.context.channel
    override val metadata: Map<String, Any?>
        get() = input.value.metadata()
    override val metrics: Map<String, List<String>>
        get() = emptyMap()
}
