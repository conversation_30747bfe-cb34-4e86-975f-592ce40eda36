package no.ruter.tranop.app.plan.stop.output.entity

import no.ruter.avro.entity.datedjourney.v2.DatedJourneyStopPointKeyV2
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.input.InputMessage
import no.ruter.tranop.app.common.mapping.output.OutputMessage
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.app.plan.stop.lifecycle.StopPointBaseRoutine
import no.ruter.tranop.app.plan.stop.lifecycle.StopPointLifeCycleConfig
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class StopPointEntityPublishRoutine(
    appInfoProperties: AppInfoProperties,
    insightService: InsightService,
    stopPointLifeCycleConfig: StopPointLifeCycleConfig,
    private val timeService: TimeService,
    private val repository: StopPointRepository,
    private val outputMapper: StopPointEntityOutputMapper,
    private val publishingService: StopPointEntityPublishingService,
) : StopPointBaseRoutine(
        appInfoProperties = appInfoProperties,
        name = LC_TYPE_PUBLISH,
        insightService = insightService,
        stopPointLifeCycleConfig,
    ) {
    override fun execute(started: OffsetDateTime): Int {
        val batchSize = 256
        val skip = 0
        var totalPublished = 0
        val seenFailedRefs = mutableSetOf<String>()
        val olderThanDateTime = started.minus(config.olderThan)

        try {
            generateSequence {
                repository.findUnpublished(AbstractQueryBuilder.Pagination(batchSize, skip), seenFailedRefs, olderThanDateTime)
            }.takeWhile { it.isNotEmpty() }
                .forEach { journeysToPublish ->
                    journeysToPublish.forEach { internal ->
                        val outputMessage = createOutputMessage(internal.stopPoint)
                        val currentRevision = internal.record.revision
                        if (!outputMessage.ok) {
                            seenFailedRefs.add(outputMessage.key)
                        }
                        outputMessage.value?.let {
                            val result = publishingService.publish(it, currentRevision)
                            if (result.isFailure) {
                                handleException(e = result.exceptionOrNull() as Exception)
                                seenFailedRefs.add(it.entityHeader.key)
                            } else {
                                totalPublished++
                            }
                        }
                    }

                    if (seenFailedRefs.size >= batchSize) {
                        throw Exception("too many errors occurred while running lifecycle, aborting...")
                    }
                }
        } catch (e: Exception) {
            handleException(e = e)
        }
        if (seenFailedRefs.isNotEmpty()) {
            logger.warn("some refs couldn't be published during lifecycle run: $seenFailedRefs")
        }
        return totalPublished
    }

    private fun createOutputMessage(
        stopPoint: DTOStopPoint,
    ): OutputMessage<
        DTOStopPoint,
        StopPointInputContext,
        DatedJourneyStopPointKeyV2,
        StopPointOutputEntityContext,
    > {
        val inputContext =
            StopPointInputContext(
                channel = channel,
                stopPoint = stopPoint,
                received = timeService.now(),
            )
        // TODO: stopPoint.ref == null
        return outputMapper.mapOutput(
            InputMessage(
                context = inputContext,
                key = stopPoint.ref,
                value = stopPoint,
            ),
        )
    }
}
