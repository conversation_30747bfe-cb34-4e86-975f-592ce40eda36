package no.ruter.tranop.app.plan.link.output.entity

import no.ruter.avro.entity.datedjourney.v2.DatedJourneyStopPointLinkKeyV2
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.input.InputMessage
import no.ruter.tranop.app.common.mapping.output.OutputMessage
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.link.db.StopPointLinkRepository
import no.ruter.tranop.app.plan.link.lifecycle.StopPointLinkBaseRoutine
import no.ruter.tranop.app.plan.link.lifecycle.StopPointLinkLifeCycleConfig
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLink
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class StopPointLinkEntityPublishRoutine(
    appInfoProperties: AppInfoProperties,
    insightService: InsightService,
    stopPointLinkLifeCycleConfig: StopPointLinkLifeCycleConfig,
    private val timeService: TimeService,
    private val repository: StopPointLinkRepository,
    private val outputMapper: StopPointLinkEntityOutputMapper,
    private val publishingService: StopPointLinkEntityPublishingService,
) : StopPointLinkBaseRoutine(
        appInfoProperties = appInfoProperties,
        name = LC_TYPE_PUBLISH,
        insightService = insightService,
        stopPointLinkLifeCycleConfig,
    ) {
    override fun execute(started: OffsetDateTime): Int {
        val batchSize = 256
        val skip = 0
        var totalPublished = 0
        val seenFailedRefs = mutableSetOf<String>()
        val olderThanDateTime = started.minus(config.olderThan)

        try {
            generateSequence {
                repository.findUnpublished(AbstractQueryBuilder.Pagination(batchSize, skip), seenFailedRefs, olderThanDateTime)
            }.takeWhile { it.isNotEmpty() }
                .forEach { journeysToPublish ->
                    journeysToPublish.forEach { internal ->
                        val outputMessage = createOutputMessage(internal.link)
                        val currentRevision = internal.record.revision
                        if (!outputMessage.ok) {
                            seenFailedRefs.add(outputMessage.key)
                        }
                        outputMessage.value?.let {
                            val result = publishingService.publish(it, currentRevision)
                            if (result.isFailure) {
                                handleException(e = result.exceptionOrNull() as Exception)
                                seenFailedRefs.add(it.entityHeader.key)
                                recordInsight("failed")
                            } else {
                                totalPublished++
                                recordInsight("published")
                            }
                        }
                    }

                    if (seenFailedRefs.size >= batchSize) {
                        throw Exception("too many errors occurred while running lifecycle, aborting...")
                    }
                }
        } catch (e: Exception) {
            handleException(e = e)
        }
        if (seenFailedRefs.isNotEmpty()) {
            logger.warn("some refs couldn't be published during lifecycle run: $seenFailedRefs")
        }
        return totalPublished
    }

    private fun createOutputMessage(
        link: DTOStopPointLink,
    ): OutputMessage<
        DTOStopPointLink,
        StopPointLinkInputContext,
        DatedJourneyStopPointLinkKeyV2,
        StopPointLinkOutputEntityContext,
    > {
        // TODO: Why are we creating an input context to create an output message?
        val inputContext =
            StopPointLinkInputContext(
                channel = RecordType.LINK.channels.output, // TODO: Set proper output channel for proper entity version.
                link = link,
                received = timeService.now(),
            )
        // TODO: link.ref == null
        return outputMapper.mapOutput(
            InputMessage(
                context = inputContext,
                key = link.ref,
                value = link,
            ),
        )
    }
}
