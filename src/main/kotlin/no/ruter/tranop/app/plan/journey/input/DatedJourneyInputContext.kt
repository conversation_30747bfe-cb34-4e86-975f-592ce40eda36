package no.ruter.tranop.app.plan.journey.input

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.mapping.toStatus
import no.ruter.tranop.app.plan.journey.JourneyUtils
import no.ruter.tranop.assignment.dto.model.value.DTOStatusCode
import no.ruter.tranop.assignment.util.toLocalDate
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.journey.dated.dto.metadata
import java.time.LocalDate
import java.time.OffsetDateTime

// TODO: Limit access to the constructor. This context should only be called by the DatedJourneyValidator
class DatedJourneyInputContext(
    val ref: String?,
    val input: DTODatedJourney?,
    override val channel: DataChannel = RecordType.DATED_JOURNEY.channels.input,
    override val recordMetadata: Boolean = false,
) : ProcessingContext() {
    val journeyTombstone = input == null
    val journeyDeleted = !journeyTombstone && (input?.deleted ?: false)
    override var skip = journeyTombstone || journeyDeleted
    override var skipReason = if (journeyTombstone) SKIP_REASON_TOMBSTONE else SKIP_REASON_DELETE

    override val traceId = input?.header?.traceId
    val messageTimestamp = input?.header?.messageTimestamp?.toOffsetDateTime()

    val cancelled: Boolean get() = input?.cancelled ?: input?.plan?.calls?.all { it.cancelled == true } ?: false
    val partiallyCancelled: Boolean get() = cancelled.not() && input?.plan?.calls?.any { it.cancelled == true } ?: false

    val omitted: Boolean get() = input?.omitted ?: input?.plan?.calls?.all { it.omitted == true } ?: false
    val partiallyOmitted: Boolean get() = omitted.not() && input?.plan?.calls?.any { it.omitted == true } ?: false

    var operatingDate: LocalDate? = input?.operatingDate?.toLocalDate()

    private val arrivals = JourneyUtils.resolveArrivals(input)
    private val departures = JourneyUtils.resolveDepartures(input)

    override val metadata: Map<String, Any?>
        get() = input.metadata()

    var firstDepartureDateTime: OffsetDateTime? = departures.firstOrNull()
    var lastDepartureDateTime: OffsetDateTime? = departures.lastOrNull()
    var lastArrivalDateTime: OffsetDateTime? = arrivals.lastOrNull()
    var firstArrivalDateTime: OffsetDateTime? = arrivals.firstOrNull()

    val journeyReferences = input?.journeyReferences
    val journeyType = input?.type
    var datedBlockRef: String? = journeyReferences?.datedBlockRef
    var externalJourneyRef: String? = journeyReferences?.externalJourneyRef
    var externalJourneyRefV2: String? = journeyReferences?.externalJourneyRefV2
    val datedJourneyV1Ref: String? = journeyReferences?.legacyDatedJourneyRef
    var datedServiceJourneyId: String? = journeyReferences?.datedServiceJourneyId ?: datedJourneyV1Ref
    var vehicleTaskRef: String? = journeyReferences?.vehicleTaskRef ?: input?.vehicleTask
    val vehicleJourneyId: String? = journeyReferences?.vehicleJourneyId
    val lineId: String? = input?.line?.lineRef
    val operatorContract = input?.operatorContracts?.firstOrNull()
    val operatorContractRef = operatorContract?.operatorContractRef

    var stored: Int = 0
    var inserted: Int = 0
    var updated: Int = 0
    var deleted: Int = 0

    var update = false

    override val summary: String
        get() = "DatedJourney: ${input?.type} / $ref"

    override val insightKey
        get() = "${channel.insightKey}.${statusDetails.toStatus().code?.value ?: DTOStatusCode.UNKNOWN}"
}
