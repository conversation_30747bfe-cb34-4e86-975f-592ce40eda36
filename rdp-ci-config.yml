include:
  - project: 'ruter-as/tranop/dev-tools/cicd-pipeline'

project:
  domain: assignment
  version: 0.16.{{ build-number }}
  description: Service responsible for assignment journeys and service deviations.

build:
  gradle:
    jdk-version: 21

    # Create 'gradle-ci' user for running Gradle.
    pre-script:
      - yum -y install sudo util-linux shadow-utils
      - groupadd -r gradle-ci -g 1000
      - useradd -u 1000 -r -g gradle-ci -m -d /home/<USER>/sbin/nologin -c "Gradle CI user" gradle-ci
      - chmod 755 /home/<USER>

    # Run Gradle as 'gradle-ci' user via 'sudo' command
    gradle-command: >
      sudo -u gradle-ci JAVA_HOME=$JAVA_HOME GRADLE_USER_HOME=$GRADLE_USER_HOME CI_JOB_TOKEN=$CI_JOB_TOKEN TRAN_RDP_CI_TOKEN=$TRAN_RDP_CI_TOKEN CI=$CI ./gradlew

    jacoco: true
    junit-report: true


deploy:
  iac:
    update:
      enabled: true
