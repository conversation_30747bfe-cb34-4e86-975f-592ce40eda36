[bundles]
kotlin-test = [
    "kotlin-test-junit",
]
shedlock = [
    "shedlock-provider-jdbc-template",
    "shedlock-spring",
]
json = [
    "jackson-annotations",
    "jackson-core",
    "jackson-databind",
    "jackson-module-kotlin",
    "rdp-json-utils",
]
tranop-models = [
    "assignment-api",
]
avro = [
    "apache-avro",
    "kafka-avro-serde",
    "kafka-schema-registry-client",
]
embedded-postgres = [
    "embedded-database-spring-test",
    "embedded-postgres",
    "embedded-postgres-binaries-linux-arm64v8",
]
rest-assured = [
    "rest-assured",
    "rest-assured-json-path",
    "rest-assured-json-schema-validator",
    "rest-assured-kotlin-extensions",
]
aws = [
    "aws-apache-client",
    "aws-auth",
    "aws-regions",
    "aws-sts",
]

[plugins]
# Kotlin
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-spring = { id = "org.jetbrains.kotlin.plugin.spring", version.ref = "kotlin" }
kotlin-kotlinter = { id = "org.jmailen.kotlinter", version.ref = "kotlin-kotlinter" }
# Spring Boot
spring-boot = { id = "org.springframework.boot", version.ref = "spring-boot" }
spring-dependencies = { id = "io.spring.dependency-management", version.ref = "spring-dependency-management" }
# Utilities
versions = { id = "com.github.ben-manes.versions", version.ref = "versions" }
test-logger = { id = "com.adarshr.test-logger", version.ref = "test-logger" }
# Code generators
jooq-codegen = { id = "nu.studer.jooq" } # No version here, since 'buildSrc' includes plugin in its implementation classpath.

[versions]
java = "21"
kotlin = "2.2.0"
spring-boot = "3.5.3"
spring-dependency-management = "1.1.7"
test-logger = "4.0.0"
kotlin-kotlinter = "5.1.1"
versions = "0.52.0"
confluent = "8.0.0"
apache-avro = "1.12.0"
shedlock = "6.9.0"
jooq = "3.20.4"
jooq-codegen = "9.0"
javax-annotation-api = "1.3.2"
rdp-kafka-streams-common = "3.0.1"
rdp-insight = "0.2.11"
rest-client = "4.12.0"
logstash-logback-encoder = "8.1"
assignment-api = "38.0.532"
rdp-json-utils = "2.0.2"
swagger-models = "2.2.30"
swagger-ui = "5.21.0"
jakarta-xml-bind-api = "4.0.2"
h2 = "2.3.232"
embedded-postgres = "2.1.0"
embedded-database-spring-test = "2.6.0"
embedded-postgres-binaries-bom = "16.4.0"
mockito-kotlin = "5.4.0"
aws-toolikt = "2.31.70"
opensearch-java = "2.23.0"
opensearch = "2.19.2"
snowflake-ingest = "4.0.0"
jaxb-api = "2.3.1"

[libraries]
spring-boot-starter = { module = "org.springframework.boot:spring-boot-starter" }
spring-boot-configuration-processor = { module = "org.springframework.boot:spring-boot-configuration-processor" }
spring-boot-starter-web = { module = "org.springframework.boot:spring-boot-starter-web" }
spring-boot-starter-security = { module = "org.springframework.boot:spring-boot-starter-security" }
spring-boot-starter-test = { module = "org.springframework.boot:spring-boot-starter-test" }
spring-kafka = { module = "org.springframework.kafka:spring-kafka" }
spring-context-support = { module = "org.springframework:spring-context-support" }
caffeine = { module = "com.github.ben-manes.caffeine:caffeine" }
javax-annotation-api = { module = "javax.annotation:javax.annotation-api", version.ref = "javax-annotation-api" }
kotlin-reflect = { module = "org.jetbrains.kotlin:kotlin-reflect" }
kotlin-test-junit = { module = "org.jetbrains.kotlin:kotlin-test-junit5", version.ref = "kotlin" }
spring-boot-starter-jooq = { module = "org.springframework.boot:spring-boot-starter-jooq" }
# Note: We "duplicate" definition of Jooq Gradle plugin here, so it can bs used in 'buildSrc' project.
jooq = { module = "org.jooq:jooq", version.ref = "jooq" } # Actual Jooq "client" library.
jooq-codegen = { module = "org.jooq:jooq-codegen", version.ref = "jooq" } # Codegen library, not plugin!
jooq-codegen-gradle = { module = "nu.studer:gradle-jooq-plugin", version.ref = "jooq-codegen" } # Plugin, not codegen library!
jooq-meta-extensions = { module = "org.jooq:jooq-meta-extensions", version.ref = "jooq" } # Meta extensions for config in `buildSrc`.
jakarta-xml-bind-api = { module = "jakarta.xml.bind:jakarta.xml.bind-api", version.ref = "jakarta-xml-bind-api" }
h2 = { module = "com.h2database:h2", version.ref = "h2" }
flyway-core = { module = "org.flywaydb:flyway-core" }
flyway-database-postgresql = { module = "org.flywaydb:flyway-database-postgresql" }
postgresql = { module = "org.postgresql:postgresql" }
shedlock-spring = { module = "net.javacrumbs.shedlock:shedlock-spring", version.ref = "shedlock" }
shedlock-provider-jdbc-template = { module = "net.javacrumbs.shedlock:shedlock-provider-jdbc-template", version.ref = "shedlock" }
embedded-postgres = { module = "io.zonky.test:embedded-postgres", version.ref = "embedded-postgres" }
embedded-database-spring-test = { module = "io.zonky.test:embedded-database-spring-test", version.ref = "embedded-database-spring-test" }
embedded-postgres-binaries-linux-arm64v8 = { module = "io.zonky.test.postgres:embedded-postgres-binaries-linux-arm64v8" }
embedded-postgres-binaries-bom = { module = "io.zonky.test.postgres:embedded-postgres-binaries-bom", version.ref = "embedded-postgres-binaries-bom" }
kafka-clients = { module = "org.apache.kafka:kafka-clients" }
kafka-schema-registry-client = { module = "io.confluent:kafka-schema-registry-client", version.ref = "confluent" }
apache-avro = { module = "org.apache.avro:avro", version.ref = "apache-avro" }
rdp-kafka-streams-common = { module = "no.ruter.rdp:rdp-kafka-streams-common", version.ref = "rdp-kafka-streams-common" }
kafka-avro-serde = { module = "io.confluent:kafka-streams-avro-serde", version.ref = "confluent" }
spring-boot-starter-logging = { module = "org.springframework.boot:spring-boot-starter-logging" }
spring-boot-starter-actuator = { module = "org.springframework.boot:spring-boot-starter-actuator" }
micrometer-registry-prometheus = { module = "io.micrometer:micrometer-registry-prometheus" }
rdp-insight = { module = "no.ruter.rdp.insight:rdp-insight", version.ref = "rdp-insight" }
rest-client = { module = "com.squareup.okhttp3:okhttp", version.ref = "rest-client" }
logstash-logback-encoder = { module = "net.logstash.logback:logstash-logback-encoder", version.ref = "logstash-logback-encoder" }
rdp-json-utils = { module = "no.ruter.rdp.common:rdp-json-utils", version.ref = "rdp-json-utils" }
jackson-core = { module = "com.fasterxml.jackson.core:jackson-core" }
jackson-databind = { module = "com.fasterxml.jackson.core:jackson-databind" }
jackson-annotations = { module = "com.fasterxml.jackson.core:jackson-annotations" }
jackson-module-kotlin = { module = "com.fasterxml.jackson.module:jackson-module-kotlin" }
assignment-api = { module = "no.ruter.tranop:assignment-api", version.ref = "assignment-api" }
swagger-ui = { module = "org.webjars:swagger-ui", version.ref = "swagger-ui" }
swagger-models = { module = "io.swagger.core.v3:swagger-models", version.ref = "swagger-models" }
kotlin-stdlib-jdk8 = { module = "org.jetbrains.kotlin:kotlin-stdlib-jdk8", version.ref = "kotlin" }
rest-assured = { module = "io.rest-assured:rest-assured" }
rest-assured-kotlin-extensions = { module = "io.rest-assured:kotlin-extensions" }
rest-assured-json-schema-validator = { module = "io.rest-assured:json-schema-validator" }
rest-assured-json-path = { module = "io.rest-assured:json-path" }
mockito-kotlin = { module = "org.mockito.kotlin:mockito-kotlin", version.ref = "mockito-kotlin" }
aws-apache-client = { module = "software.amazon.awssdk:apache-client", version.ref = "aws-toolikt" }
aws-regions = { module = "software.amazon.awssdk:regions", version.ref = "aws-toolikt" }
aws-auth = { module = "software.amazon.awssdk:auth", version.ref = "aws-toolikt" }
aws-sts = { module = "software.amazon.awssdk:sts", version.ref = "aws-toolikt" }
opensearch-client = { module = "org.opensearch.client:opensearch-java", version.ref = "opensearch-java" }
opensearch = { module = "org.opensearch:opensearch", version.ref = "opensearch" }
opensearch-netty = { module = "org.opensearch.plugin:transport-netty4-client", version.ref = "opensearch" }
opensearch-rest-client = { module = "org.opensearch.client:opensearch-rest-client", version.ref = "opensearch" }
kotlin-coroutines = "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.10.2"
jcl-over-slf4j = { module = "org.slf4j:jcl-over-slf4j" }
okhttp3-mockwebserver = { module = "com.squareup.okhttp3:mockwebserver", version.ref = "rest-client" }
snowflake-ingest = { module = "net.snowflake:snowflake-ingest-sdk", version.ref = "snowflake-ingest" }
jaxb-api = { module = "javax.xml.bind:jaxb-api", version.ref = "jaxb-api" }
